import traceback

from API import Re
from collections import OrderedDict

from loguru import logger
from bs4 import BeautifulSoup
from API.Questionbank import *
import difflib
import json
import re
import time
import requests
from API.Xuan import match_answer, sort_answers


def tiankong_quchong(text):
    items = text.split("###")
    unique_items = list(OrderedDict.fromkeys(item.strip() for item in items))
    result = "###".join(unique_items)

    return result


class StaratWorkTaks:
    def __init__(
        self, session, couserid, classid, cpi, listid, jobid, kcname, username, task_type="chapter_test"
    ):
        self.session = session
        self.couserid = couserid
        self.kcname = kcname
        self.classid = classid
        self.cpi = cpi
        self.listid = listid
        self.jobid = jobid
        self.username = username
        self.userid = self.session.Uid()
        # 添加AI答题统计变量
        self.ai_answer_count = 0  # AI回答的题目数量
        self.total_question_count = 0  # 总题目数量
        # 添加任务类型标识，用于区分章节测验和作业
        self.task_type = task_type  # "chapter_test" 或 "assignment"

    def Html_Wkrk(self, html):
        self.html = BeautifulSoup(html, "html.parser")

        # 初始化AI答题统计变量
        self.ai_answer_count = 0
        self.total_question_count = 0

        # 设置平台ID，供Xuan函数使用
        self.platform_id = getattr(self.session, "cid", 0)

        # 只在非9004平台ID时输出平台ID日志
        if not hasattr(self.session, "cid") or self.session.cid != 9004:
            logger.info(f"ID:{self.username},当前平台ID: {self.platform_id}")

        # 检查当前平台ID，扩展AI答题支持到更多平台
        self.use_ai_answers = False
        if hasattr(self.session, "cid") and self.session.cid in [9000, 9001, 9003, 9004]:
            self.use_ai_answers = True
            # 只在非9004平台ID时输出AI答题启用日志
            if self.session.cid != 9004:
                logger.info(f"ID:{self.username},平台ID {self.session.cid}，启用AI答题功能")

        # 检查是否已提交或已批阅
        is_submitted = False
        if (
            "已批阅" in self.html.find("title").text.strip()
            or "待批阅" in self.html.find("title").text.strip()
        ):
            is_submitted = True
            logger.success(f"ID:{self.username},本章节已提交")

        # 检查是否有正确答案标记
        correct_answers = self.html.select("i.fontWeight.custom-style")
        if correct_answers and any("正确答案" in elem.text for elem in correct_answers):
            is_submitted = True
            logger.success(f"ID:{self.username},找到正确答案标记，本章节已提交过")

        if is_submitted:
            # 尝试获取作业标题
            title_elem = self.html.select_one(
                ".mark_title, h3.title, .titTxt, h1, h2.title"
            )
            title = title_elem.text.strip() if title_elem else "未知作业"

            # 添加"已提交过"标记到标题
            title = f"{title} (已提交过)"

            # 如果session有cid属性且为9004，更新进度信息
            if hasattr(self.session, "cid") and self.session.cid == 9004:
                try:
                    # 使用更安全的导入方式
                    import sys
                    import importlib.util

                    logger.info(f"ID:{self.username},尝试导入data.Porgres模块")

                    # 尝试直接导入
                    try:
                        from data.Porgres import StartProces

                        logger.info(
                            f"ID:{self.username},成功导入data.Porgres.StartProces"
                        )
                    except ImportError as e:
                        logger.warning(f"ID:{self.username},直接导入失败: {str(e)}")

                        # 尝试使用importlib导入
                        try:
                            spec = importlib.util.find_spec("data.Porgres")
                            if spec is None:
                                logger.error(
                                    f"ID:{self.username},找不到data.Porgres模块"
                                )
                            else:
                                module = importlib.util.module_from_spec(spec)
                                spec.loader.exec_module(module)
                                StartProces = module.StartProces
                                logger.info(
                                    f"ID:{self.username},使用importlib成功导入StartProces"
                                )
                        except Exception as e2:
                            logger.error(
                                f"ID:{self.username},importlib导入失败: {str(e2)}"
                            )
                            return

                    # 创建进度对象并更新进度
                    logger.info(f"ID:{self.username},创建StartProces对象")
                    p = StartProces(
                        self.session,
                        [
                            {
                                "kcname": "作业任务",
                                "courseid": self.couserid,
                                "clazzid": self.classid,
                                "cpi": self.cpi,
                            }
                        ],
                    )

                    logger.info(
                        f"ID:{self.username},调用get_platform_9004_progress方法"
                    )
                    progress, remarks = p.get_platform_9004_progress(
                        homework_title=title, status="已提交过"
                    )

                    # logger.info(f"ID:{self.username},进度更新: {progress}, {remarks}")

                    # 更新数据库
                    from Config.UserSql import OrderProcessorsql

                    pool = OrderProcessorsql()
                    # 从session中获取oid
                    if hasattr(self.session, "oid"):
                        logger.info(
                            f"ID:{self.username},更新数据库记录, oid: {self.session.oid}"
                        )
                        pool.update_order(
                            f"update qingka_wangke_order set process = '{progress}',remarks = '{remarks}' where oid = '{self.session.oid}'"
                        )
                except Exception as e:
                    logger.error(f"ID:{self.username},更新进度信息异常: {str(e)}")
                    traceback.print_exc()
            return
        else:
            try:
                # 尝试获取常规作业页面的参数
                try:
                    self.workAnswerId = self.html.find(
                        "input", {"id": "workAnswerId"}
                    ).get("value")
                    self.totalQuestionNum = self.html.find(
                        "input", {"id": "totalQuestionNum"}
                    ).get("value")
                    self.old = self.html.find("input", {"id": "oldWorkId"}).get("value")
                    self.workRelationId = self.html.find(
                        "input", {"id": "workRelationId"}
                    ).get("value")
                    self.enc_work = self.html.find("input", {"id": "enc_work"}).get(
                        "value"
                    )
                except AttributeError:
                    # 尝试获取作业页面的参数 (平台ID 9004)
                    logger.info(f"ID:{self.username},尝试解析作业页面参数")
                    # 查找表单中的隐藏字段
                    self.workAnswerId = (
                        self.html.find("input", {"name": "workAnswerId"}).get("value")
                        if self.html.find("input", {"name": "workAnswerId"})
                        else ""
                    )
                    self.totalQuestionNum = (
                        self.html.find("input", {"name": "totalQuestionNum"}).get(
                            "value"
                        )
                        if self.html.find("input", {"name": "totalQuestionNum"})
                        else ""
                    )
                    self.old = (
                        self.html.find("input", {"id": "old"}).get("value")
                        if self.html.find("input", {"id": "old"})
                        else ""
                    )
                    self.workRelationId = (
                        self.html.find("input", {"name": "workRelationId"}).get("value")
                        if self.html.find("input", {"name": "workRelationId"})
                        else ""
                    )
                    self.enc_work = (
                        self.html.find("input", {"id": "enc_work"}).get("value")
                        if self.html.find("input", {"id": "enc_work"})
                        else ""
                    )

                    # 如果仍然无法获取关键参数，尝试从其他位置提取
                    if not self.workAnswerId:
                        self.workAnswerId = (
                            self.html.find("input", {"id": "answerId"}).get("value")
                            if self.html.find("input", {"id": "answerId"})
                            else ""
                        )
                    if not self.enc_work:
                        self.enc_work = (
                            self.html.find("input", {"id": "enc"}).get("value")
                            if self.html.find("input", {"id": "enc"})
                            else ""
                        )

                self.params = dict()
                answerwqbid = list()

                # 查找题目元素，支持多种可能的类名
                question_divs = self.html.findAll(
                    "div", {"class": "Py-mian1 singleQuesId"}
                )
                if not question_divs:
                    question_divs = self.html.findAll("div", {"class": "questionLi"})
                if not question_divs:
                    question_divs = self.html.findAll("div", {"class": "TiMu"})
                if not question_divs:
                    question_divs = self.html.findAll(
                        "div", {"class": "padBom50 questionLi fontLabel singleQuesId"}
                    )

                # 记录处理的题目数量
                processed_questions = 0

                # 设置最大处理题目数量，避免处理过多题目导致超时
                max_questions = len(question_divs)

                # 只在非9004平台ID时输出题目数量日志
                if not hasattr(self.session, "cid") or self.session.cid != 9004:
                    logger.info(f"ID:{self.username},找到{len(question_divs)}个题目")

                for quest_index, quest in enumerate(question_divs):
                    try:
                        # 记录当前处理的题目索引，仅在非9004平台ID时输出详细日志
                        if not hasattr(self.session, "cid") or self.session.cid != 9004:
                            logger.info(
                                f"ID:{self.username},开始处理第 {quest_index + 1}/{len(question_divs)} 个题目"
                            )

                        # 尝试多种方式获取题目ID
                        try:
                            self.qid = quest.get("data")
                            if not self.qid:
                                self.qid = quest.get("id", "").replace("question", "")
                            if not self.qid:
                                self.qid = (
                                    quest.find(
                                        "input",
                                        {"name": re.compile(r"^answertype\d+$")},
                                    )
                                    .get("name")
                                    .replace("answertype", "")
                                )
                        except (AttributeError, TypeError):
                            # 如果无法获取题目ID，生成一个随机ID
                            self.qid = f"q{len(answerwqbid) + 1}"
                            logger.warning(
                                f"ID:{self.username},无法获取题目ID，使用临时ID:{self.qid}"
                            )

                        # 尝试多种方式获取题目内容
                        try:
                            question_div = quest.find(
                                "h3",
                                {"class": "mark_name colorDeep fontLabel workTextWrap"},
                            )
                            if not question_div:
                                question_div = quest.find(
                                    "div", {"class": "Py-m1-title fs16 fontLabel"}
                                )
                            if not question_div:
                                question_div = quest.find(
                                    "div", {"class": "Zy_TItle clearfix"}
                                )
                            if not question_div:
                                question_div = quest.find(
                                    "div", {"class": "mark_name colorDeep"}
                                )
                            if not question_div:
                                question_div = quest.find(
                                    "h3", {"class": "mark_name colorDeep"}
                                )

                            # 获取题目文本，去除序号和题型标签
                            if question_div:
                                question_text = question_div.text.strip()
                                # 移除题目序号
                                question_text = re.sub(r"^\d+\.", "", question_text)

                                # 检查是否包含题型标签
                                has_type_label = False
                                if re.search(
                                    r"^\s*\([^)]*选择题[^)]*\)", question_text
                                ) or re.search(r"^\s*（[^）]*选择题[^）]*）", question_text):
                                    has_type_label = True
                                elif re.search(
                                    r"^\s*\([^)]*判断题[^)]*\)", question_text
                                ) or re.search(r"^\s*（[^）]*判断题[^）]*）", question_text):
                                    has_type_label = True
                                elif re.search(
                                    r"^\s*\([^)]*填空题[^)]*\)", question_text
                                ) or re.search(r"^\s*（[^）]*填空题[^）]*）", question_text):
                                    has_type_label = True
                                elif re.search(
                                    r"^\s*\([^)]*简答题[^)]*\)", question_text
                                ) or re.search(r"^\s*（[^）]*简答题[^）]*）", question_text):
                                    has_type_label = True

                                # 移除题型标签和分数标记
                                # 1. 移除题型标签，如"(单选题, 2分)"
                                question_text = re.sub(
                                    r"^\s*\(\s*(?:单选题|多选题|判断题|填空题|简答题)[\s\,\.]*\d*分?\s*\)\s*",
                                    "",
                                    question_text,
                                )
                                question_text = re.sub(
                                    r"^\s*（\s*(?:单选题|多选题|判断题|填空题|简答题)[\s\,\.]*\d*分?\s*）\s*",
                                    "",
                                    question_text,
                                )

                                # 2. 移除题目末尾的分数标记，如"(5.0)"
                                question_text = re.sub(
                                    r"\s*\(\s*\d+(\.\d+)?\s*\)\s*$", "", question_text
                                )
                                question_text = re.sub(
                                    r"\s*（\s*\d+(\.\d+)?\s*）\s*$", "", question_text
                                )

                                # 3. 移除span标签中的题型信息
                                # 检查是否有题型前缀元素
                                type_span = question_div.select_one(".colorShallow")
                                if type_span:
                                    # 获取span文本
                                    type_text = type_span.text.strip()
                                    # 从题目文本中移除这部分
                                    question_text = question_text.replace(
                                        type_text, ""
                                    ).strip()

                                question = question_text.strip()

                                # 为AI答题保存原始题目文本（不使用Re.strip_title处理）
                                self.original_question = question

                                # 添加调试日志，记录原始题目和处理后的题目，仅在DEBUG级别
                                # if logger.level("DEBUG").no <= logger.level("INFO").no:
                                #     logger.debug(
                                #         f"ID:{self.username},原始题目: {question}"
                                #     )
                                #     logger.debug(
                                #         f"ID:{self.username},处理后题目: {self.question}"
                                #     )

                        except AttributeError:
                            question = ""
                            logger.warning(f"ID:{self.username},无法获取题目内容")

                        # 尝试多种方式获取题目类型
                        try:
                            qtp_input = quest.find(
                                "input", {"name": f"answertype{self.qid}"}
                            )
                            if qtp_input:
                                self.qtp = int(qtp_input.get("value").strip())
                            else:
                                # 尝试从题目元素的class或其他属性推断题型
                                if quest.find(
                                    "ul", {"class": "Zy_ulTop"}
                                ):  # 选择题特征
                                    self.qtp = 0 if "单选题" in question else 1
                                elif quest.find("textarea"):  # 简答题特征
                                    self.qtp = 4
                                elif quest.find(
                                    "div", {"class": "Py_answer"}
                                ):  # 判断题特征
                                    self.qtp = 3
                                else:
                                    self.qtp = 0  # 默认为单选题
                        except (AttributeError, ValueError):
                            self.qtp = 0  # 默认为单选题
                            logger.warning(
                                f"ID:{self.username},无法获取题目类型，默认为单选题"
                            )

                        # 处理题目中的图片
                        if question_div and question_div.find_all("img", src=True):
                            question += "".join(
                                [
                                    img["src"]
                                    for img in question_div.find_all("img", src=True)
                                    if img.get("src")
                                ]
                            )

                        # 检查是否包含填空符号，使用适当的处理方法
                        has_blank_brackets = False
                        if re.search(r"（\s+）|（\s*）|\(\s+\)|\(\s*\)", question):
                            has_blank_brackets = True
                            logger.debug(
                                f"ID:{self.username},题目包含填空符号，使用保留填空符号的方法处理"
                            )
                            self.question = Re.strip_title(question)
                        else:
                            self.question = Re.strip_title(question)

                        logger.debug(f"{self.qtp}-{self.question}")

                        # 添加调试日志，记录处理后的题目
                        logger.debug(f"ID:{self.username},处理后题目: {self.question}")

                        # 清理问题文本
                        question_text = self.original_question
                        if not question_text:
                            question_text = self.question
                        logger.debug(f"{self.qtp}-{question_text}")

                        # 初始化答案变量
                        answer = None
                        self.qnum = 0
                        use_ai_for_current_question = False

                        # 记录总题目数
                        self.total_question_count += 1

                        # 按优先级顺序尝试答题：主题库 -> 备用题库 -> AI
                        if self.qtp in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14]:  # 支持的题型
                            # 步骤1: 尝试使用主题库
                            # 仅在非9004平台ID时输出详细日志
                            if (
                                not hasattr(self.session, "cid")
                                or self.session.cid != 9004
                            ):
                                logger.info(
                                    f"ID:{self.username},尝试使用主题库查询答案"
                                )

                            try:
                                # 使用主题库查询答案
                                answer = questionbank(
                                    question_text,
                                    self.qtp,
                                    timeout=10,
                                    platform_id=self.platform_id,
                                )

                                # 如果答案是"未收录答案"，则跳过当前题目
                                if answer == "未收录答案":
                                    logger.warning(
                                        f"ID:{self.username},题目在题库中但未收录答案，跳过此题"
                                    )
                                    continue

                                # 如果主题库找到了答案
                                if answer:
                                    success = False

                                    # 根据题型处理答案
                                    if self.qtp == 0 or self.qtp == 1:  # 选择题
                                        # 匹配选项
                                        matched_option = self.Xuan(quest, answer)
                                        if matched_option:
                                            logger.success(
                                                f"ID:{self.username},主题库答案匹配成功: {matched_option}"
                                            )
                                            self.params[f"answer{self.qid}"] = (
                                                matched_option
                                            )
                                            self.params[f"answer{self.qid}_name"] = (
                                                f"{matched_option}-{answer}"
                                            )
                                            success = True
                                    elif self.qtp == 2:  # 填空题
                                        # 处理填空题答案
                                        logger.success(
                                            f"ID:{self.username},主题库填空题答案: {answer}"
                                        )
                                        self.params[f"answer{self.qid}"] = answer
                                        self.params[f"answer{self.qid}_name"] = (
                                            f"{answer}"
                                        )
                                        success = True
                                    elif self.qtp == 3:  # 判断题
                                        # 处理判断题答案
                                        judge_result = (
                                            "true"
                                            if "正确" in answer
                                            or "对" in answer
                                            or "true" in answer.lower()
                                            else "false"
                                        )
                                        logger.success(
                                            f"ID:{self.username},主题库判断题答案: {judge_result}"
                                        )
                                        self.params[f"answer{self.qid}"] = judge_result
                                        self.params[f"answer{self.qid}_name"] = (
                                            f"{judge_result}"
                                        )
                                        success = True
                                    elif self.qtp in [4, 5, 6, 7, 8, 9, 14]:  # 简答题
                                        # 处理简答题答案
                                        answers = f"<p>{answer}</p>"
                                        logger.success(
                                            f"ID:{self.username},主题库简答题答案: {answer[:30]}..."
                                        )
                                        self.params[f"answer{self.qid}"] = answers
                                        self.params[f"answer{self.qid}_name"] = answers
                                        success = True

                                    # 如果成功处理了答案，继续下一题
                                    if success:
                                        # 添加到已处理题目ID列表
                                        answerwqbid.append(self.qid)
                                        self.params[f"answertype{self.qid}"] = str(
                                            self.qtp
                                        )

                                        # 增加已处理题目计数
                                        processed_questions += 1
                                        logger.info(
                                            f"ID:{self.username},已处理 {processed_questions}/{len(question_divs)} 个题目"
                                        )
                                        continue
                                    else:
                                        logger.warning(
                                            f"ID:{self.username},主题库答案处理失败，尝试备用题库"
                                        )
                            except Exception as e:
                                logger.error(
                                    f"ID:{self.username},主题库查询或答案处理异常: {str(e)}"
                                )

                            # 步骤2: 尝试使用备用题库
                            # 仅在非9004平台ID时输出详细日志
                            if (
                                not hasattr(self.session, "cid")
                                or self.session.cid != 9004
                            ):
                                logger.info(
                                    f"ID:{self.username},尝试使用备用题库查询答案"
                                )

                            try:
                                backup_answer, _ = questionbank2(
                                    question_text,
                                    timeout=10,
                                    platform_id=self.platform_id,
                                )

                                # 如果备用题库找到了答案
                                if backup_answer and backup_answer != "未收录答案":
                                    success = False

                                    # 根据题型处理答案
                                    if self.qtp == 0 or self.qtp == 1:  # 选择题
                                        matched_option = self.Xuan(quest, backup_answer)
                                        if matched_option:
                                            logger.success(
                                                f"ID:{self.username},备用题库答案匹配成功: {matched_option}"
                                            )
                                            self.params[f"answer{self.qid}"] = (
                                                matched_option
                                            )
                                            self.params[f"answer{self.qid}_name"] = (
                                                f"{matched_option}-{backup_answer}"
                                            )
                                            success = True
                                    elif self.qtp == 2:  # 填空题
                                        logger.success(
                                            f"ID:{self.username},备用题库填空题答案: {backup_answer}"
                                        )
                                        self.params[f"answer{self.qid}"] = backup_answer
                                        self.params[f"answer{self.qid}_name"] = (
                                            f"{backup_answer}"
                                        )
                                        success = True
                                    elif self.qtp == 3:  # 判断题
                                        judge_result = (
                                            "true"
                                            if "正确" in backup_answer
                                            or "对" in backup_answer
                                            or "true" in backup_answer.lower()
                                            else "false"
                                        )
                                        logger.success(
                                            f"ID:{self.username},备用题库判断题答案: {judge_result}"
                                        )
                                        self.params[f"answer{self.qid}"] = judge_result
                                        self.params[f"answer{self.qid}_name"] = (
                                            f"{judge_result}"
                                        )
                                        success = True
                                    elif self.qtp in [4, 5, 6, 7, 8, 9, 14]:  # 简答题
                                        answers = f"<p>{backup_answer}</p>"
                                        logger.success(
                                            f"ID:{self.username},备用题库简答题答案: {backup_answer[:30]}..."
                                        )
                                        self.params[f"answer{self.qid}"] = answers
                                        self.params[f"answer{self.qid}_name"] = answers
                                        success = True

                                    # 如果成功处理了答案，继续下一题
                                    if success:
                                        # 添加到已处理题目ID列表
                                        answerwqbid.append(self.qid)
                                        self.params[f"answertype{self.qid}"] = str(
                                            self.qtp
                                        )

                                        # 增加已处理题目计数
                                        processed_questions += 1
                                        logger.info(
                                            f"ID:{self.username},已处理 {processed_questions}/{len(question_divs)} 个题目"
                                        )
                                        continue
                                    else:
                                        logger.warning(
                                            f"ID:{self.username},备用题库答案处理失败，尝试AI答题"
                                        )
                            except Exception as e:
                                logger.error(
                                    f"ID:{self.username},备用题库查询或答案处理异常: {str(e)}"
                                )

                            # 步骤3: 如果允许AI答题，尝试使用AI
                            if self.use_ai_answers:
                                # 仅在非9004平台ID时输出详细日志
                                if (
                                    not hasattr(self.session, "cid")
                                    or self.session.cid != 9004
                                ):
                                    logger.info(
                                        f"ID:{self.username},尝试使用AI生成答案"
                                    )

                                self.ai_answer_count += 1

                                try:
                                    success = False

                                    # 根据题型使用AI生成答案
                                    if self.qtp == 0 or self.qtp == 1:  # 选择题
                                        answers, htmlanswer = (
                                            self.get_ai_answer_for_choice(
                                                quest, self.original_question
                                            )
                                        )
                                        if answers:
                                            # logger.success(
                                            #     f"ID:{self.username},使用AI生成答案: {answers}"
                                            # )
                                            self.params[f"answer{self.qid}"] = answers
                                            self.params[f"answer{self.qid}_name"] = (
                                                f"{answers}-AI选择: {answers}"
                                            )
                                            # logger.success(
                                            #     f"answer:{answers}-{htmlanswer}"
                                            # )
                                            success = True
                                    elif self.qtp == 2:  # 填空题
                                        ai_answer = self._generate_ai_answer_for_blank(
                                            self.original_question
                                        )
                                        if ai_answer:
                                            # 处理填空题答案
                                            logger.success(
                                                f"ID:{self.username},AI生成填空题答案成功"
                                            )

                                            # 查找所有填空输入框
                                            input_fields = quest.findAll(
                                                "input",
                                                {
                                                    "class": "blankInp2 answerInput escapeInput"
                                                },
                                            )
                                            if not input_fields:
                                                # 尝试其他可能的选择器
                                                input_fields = quest.findAll(
                                                    "input",
                                                    {
                                                        "class": re.compile(
                                                            r".*answerInput.*"
                                                        )
                                                    },
                                                )

                                            # 处理每个填空
                                            answers = ai_answer.split("###")
                                            for a, i in enumerate(input_fields):
                                                tkid = i.get("id").replace("answer", "")
                                                try:
                                                    tk_answer = (
                                                        answers[a]
                                                        if a < len(answers)
                                                        else ai_answer
                                                    )
                                                except:
                                                    tk_answer = ai_answer

                                                # 设置答案
                                                self.params[f"answer{tkid}"] = tk_answer
                                                self.params[f"answerEditor{tkid}"] = (
                                                    f"<p>{tk_answer}</p>"
                                                )

                                            # 获取tiankongsize参数
                                            try:
                                                tiankongsize_input = quest.find(
                                                    "input",
                                                    {"name": f"tiankongsize{self.qid}"},
                                                )
                                                if tiankongsize_input:
                                                    tiankongsize = (
                                                        tiankongsize_input.get("value")
                                                    )
                                                    self.params[
                                                        f"tiankongsize{self.qid}"
                                                    ] = tiankongsize
                                            except:
                                                pass

                                            success = True
                                    elif self.qtp == 3:  # 判断题
                                        ai_answer = self._generate_ai_answer_for_judge(
                                            self.original_question
                                        )
                                        if ai_answer:
                                            logger.success(
                                                f"ID:{self.username},AI生成判断题答案成功"
                                            )
                                            self.params[f"answer{self.qid}"] = ai_answer
                                            self.params[f"answer{self.qid}_name"] = (
                                                ai_answer
                                            )
                                            # logger.info(f"answer:{ai_answer}")
                                            success = True
                                    elif self.qtp in [4, 5, 6, 7, 8, 9, 14]:  # 简答题
                                        ai_answer = self._generate_ai_answer_for_essay(
                                            self.original_question
                                        )
                                        if ai_answer:
                                            answers = f"<p>{ai_answer}</p>"
                                            logger.success(
                                                f"ID:{self.username},AI生成简答题答案成功"
                                            )
                                            self.params[f"answer{self.qid}"] = answers
                                            self.params[f"answer{self.qid}_name"] = (
                                                answers
                                            )
                                            success = True

                                    # 如果成功处理了答案，继续下一题
                                    if success:
                                        # 添加到已处理题目ID列表
                                        answerwqbid.append(self.qid)
                                        self.params[f"answertype{self.qid}"] = str(
                                            self.qtp
                                        )

                                        # 增加已处理题目计数
                                        processed_questions += 1
                                        logger.info(
                                            f"ID:{self.username},已处理 {processed_questions}/{len(question_divs)} 个题目"
                                        )
                                        continue
                                    else:
                                        logger.warning(
                                            f"ID:{self.username},AI答题失败，跳过此题"
                                        )
                                except Exception as e:
                                    logger.error(
                                        f"ID:{self.username},AI答题异常: {str(e)}"
                                    )
                            else:
                                logger.warning(
                                    f"ID:{self.username},不允许使用AI答题，跳过此题"
                                )
                        else:
                            logger.warning(
                                f"ID:{self.username},不支持的题型: {self.qtp}，跳过此题"
                            )

                        # 如果所有方法都失败，跳过此题
                        logger.warning(
                            f"ID:{self.username},所有答题方法均失败，跳过此题"
                        )
                        continue

                        # 添加到已处理题目ID列表
                        answerwqbid.append(self.qid)
                        self.params[f"answertype{self.qid}"] = str(self.qtp)

                        # 增加已处理题目计数
                        processed_questions += 1
                        logger.info(
                            f"ID:{self.username},已处理 {processed_questions}/{len(question_divs)} 个题目"
                        )

                        # 每处理5个题目，暂停0.5秒，避免过快导致服务器拒绝
                        if processed_questions % 5 == 0:
                            logger.info(f"ID:{self.username},暂停0.5秒")
                            time.sleep(0.5)
                    except Exception as e:
                        logger.error(
                            f"ID:{self.username},处理题目 {quest_index + 1} 异常: {str(e)}"
                        )
                        traceback.print_exc()

                # 如果没有找到任何题目，记录警告
                if not answerwqbid:
                    logger.warning(f"ID:{self.username},未找到任何题目")
                    return

                # 仅在处理完成时输出一次总结日志
                logger.info(
                    f"ID:{self.username},所有题目处理完成，共 {processed_questions}/{len(question_divs)} 个"
                )

                self.params["answerwqbid"] = ",".join(answerwqbid) + ","

                # 根据任务类型选择不同的提交方法
                if self.task_type == "chapter_test":
                    # 章节测验使用专用提交方法
                    # 检查答题完成度，决定使用提交模式还是保存模式
                    is_complete = processed_questions == len(question_divs)
                    if is_complete:
                        logger.info(f"ID:{self.username},答题完整，使用提交模式")
                        self.PostDoChapterTest(save_mode=False)
                    else:
                        logger.warning(f"ID:{self.username},答题不完整({processed_questions}/{len(question_divs)})，使用保存模式")
                        self.PostDoChapterTest(save_mode=True)
                else:
                    # 作业使用原有提交方法
                    self.PostDo()
            except Exception as e:
                traceback.print_exc()
                logger.info(
                    f"ID:{self.username},章节老师未设置内容或解析失败: {str(e)}"
                )

    def Xuan(self, quest, answers, bidui=0.95):
        """
        选择题答案匹配函数，将题库答案与选项进行匹配
        :param quest: 题目元素
        :param answers: 题库答案
        :param bidui: 匹配阈值
        :return: 匹配到的选项或None
        """
        try:
            # 如果答案为None或"未收录答案"，直接返回None
            if not answers or answers == "未收录答案":
                logger.warning(f"ID:{self.username},答案为空或未收录，无法匹配选项")
                return None

            # 获取所有选项
            options = {}

            # 记录完整的题目HTML用于调试
            quest_html = str(quest)
            # logger.debug(f"ID:{self.username},题目HTML: {quest_html[:200]}...")

            # 尝试多种选择器来获取选项
            option_elements = []

            # 常规选择器
            selectors = [
                ".clearfix li",
                ".Zy_ulTop li",
                ".ulTop li",
                "li",
                ".stem_answer .clearfix li",
                ".answerBg",
                "[class*='answer']",
                ".answer_p",
            ]

            # 针对特殊平台的特殊选择器
            # 检查是否有platform_id属性，没有则默认为0
            platform_id = getattr(self, "platform_id", 0)
            if platform_id == 9004:
                selectors.extend(
                    [
                        ".stem_answer li",
                        ".answer_p li",
                        ".answer_options li",
                        ".answer_list li",
                        ".option",
                    ]
                )

            # 尝试所有选择器
            for selector in selectors:
                option_elements = quest.select(selector)
                if option_elements:
                    # logger.debug(
                    #     f"ID:{self.username},使用选择器 '{selector}' 找到 {len(option_elements)} 个选项"
                    # )
                    break

            # 提取选项内容
            if option_elements:
                for i, option in enumerate(option_elements):
                    option_label = chr(65 + i)  # A, B, C, ...
                    option_text = Re.strip_options(option.text.strip())
                    options[option_label] = option_text
                    # logger.debug(
                    #     f"ID:{self.username},选项 {option_label}: {option_text}"
                    # )

            # 如果没有找到选项，尝试从整个题目元素中提取
            if not options:
                logger.warning(
                    f"ID:{self.username},未通过选择器找到选项，尝试从题目元素中提取"
                )

                # 尝试查找选项模式：A. 文本 B. 文本 等
                option_patterns = [
                    r"([A-D])[\.、\s\)]([^A-D<]+)",
                    r"([A-D])[\.、\s\)](.*?)(?=\s*[A-D][\.、\s\)]|$)",
                    r"选项\s*([A-D])[：:]\s*([^<]+)",
                    r"([A-D])[：:]\s*([^<]+)",
                ]

                for pattern in option_patterns:
                    option_matches = re.findall(pattern, quest_html)
                    if option_matches:
                        logger.debug(
                            f"ID:{self.username},使用模式 '{pattern}' 找到 {len(option_matches)} 个选项"
                        )
                        for match in option_matches:
                            label, text = match
                            options[label] = text.strip()
                            logger.debug(
                                f"ID:{self.username},选项 {label}: {text.strip()}"
                            )
                        break

                # 特殊处理：可能有特殊的选项格式
                if not options:
                    logger.warning(f"ID:{self.username},尝试特殊选项提取")
                    # 查找所有可能的选项文本
                    option_divs = quest.select("div")
                    for i, div in enumerate(option_divs):
                        div_text = div.text.strip()
                        if div_text and len(div_text) > 2:  # 忽略太短的文本
                            # 检查是否以A.、B.等开头
                            option_match = re.match(r"^([A-D])[\.、\s\)]", div_text)
                            if option_match:
                                label = option_match.group(1)
                                text = div_text[2:].strip()
                                options[label] = text
                                logger.debug(
                                    f"ID:{self.username},从div中提取选项 {label}: {text}"
                                )

            # 如果仍然没有找到选项，返回None
            if not options:
                logger.warning(f"ID:{self.username},未找到选项")
                return None

            # 记录找到的选项，用于调试
            logger.info(f"ID:{self.username},找到选项: {options}")
            logger.info(f"ID:{self.username},题库答案: {answers}")

            # 检查答案是否直接是选项字母
            if re.match(r"^[A-Z]+$", answers):
                logger.success(f"ID:{self.username},答案直接是选项字母: {answers}")
                return answers

            # 检查是否包含选项字母
            option_pattern = re.compile(r"\b([A-Z])[\.、\s\)]")
            option_matches = option_pattern.findall(answers)
            if option_matches:
                option_letters = "".join(option_matches)
                logger.success(
                    f"ID:{self.username},从答案中提取到选项字母: {option_letters}"
                )
                return option_letters

            # 增强匹配逻辑：检查答案是否直接包含选项标记（如"E 物理性、化学性..."）
            option_prefix_pattern = re.compile(r"^([A-Z])\s+")
            prefix_match = option_prefix_pattern.search(answers)
            if prefix_match:
                option_letter = prefix_match.group(1)
                logger.success(
                    f"ID:{self.username},从答案前缀提取到选项字母: {option_letter}"
                )
                return option_letter

            # 完全匹配：检查选项内容是否与答案完全匹配
            for label, text in options.items():
                if text.strip() == answers.strip():
                    logger.success(
                        f"ID:{self.username},选项内容与答案完全匹配: {label}"
                    )
                    return label

            # 如果是多选题，尝试匹配多个选项
            if self.qtp == 1:
                # 预处理答案 - 统一使用###分隔符
                processed_answers = answers
                if "\n" in answers and "###" not in answers:
                    logger.info(
                        f"ID:{self.username},检测到换行符分隔的多选题答案，转换为###分隔"
                    )
                    processed_answers = answers.replace("\n", "###")

                # 处理其他常见分隔符
                if "###" not in processed_answers:
                    for sep in [",", "，", ";", "；", "、"]:
                        if (
                            sep in processed_answers
                            and len(processed_answers.split(sep)) > 1
                        ):
                            logger.info(
                                f"ID:{self.username},检测到'{sep}'分隔的多选题答案，转换为###分隔"
                            )
                            processed_answers = processed_answers.replace(sep, "###")
                            break

                # 使用###分隔符拆分答案
                if "###" in processed_answers:
                    answer_parts = processed_answers.split("###")
                    matched_options = []

                    for part in answer_parts:
                        part = part.strip()
                        if not part:
                            continue

                        # 尝试匹配每个部分与选项
                        for label, text in options.items():
                            # 完全匹配
                            if text.strip() == part:
                                matched_options.append(label)
                                logger.info(
                                    f"ID:{self.username},多选题部分完全匹配: {label} - {part}"
                                )
                                break

                            # 包含匹配
                            elif text.strip() in part or part in text.strip():
                                matched_options.append(label)
                                logger.info(
                                    f"ID:{self.username},多选题部分包含匹配: {label} - {part}"
                                )
                                break

                            # 去除标点后比较
                            else:
                                cleaned_part = re.sub(r"[^\w\s]", "", part).lower()
                                cleaned_text = re.sub(r"[^\w\s]", "", text).lower()

                                if (
                                    cleaned_text == cleaned_part
                                    or cleaned_text in cleaned_part
                                    or cleaned_part in cleaned_text
                                ):
                                    matched_options.append(label)
                                    logger.info(
                                        f"ID:{self.username},多选题部分清洗后匹配: {label} - {part}"
                                    )
                                    break

                    # 如果找到匹配的选项，返回排序后的字母组合
                    if matched_options:
                        result = "".join(sorted(matched_options))
                        logger.success(f"ID:{self.username},多选题匹配成功: {result}")
                        return result

                # 如果使用分隔符拆分匹配失败，继续尝试传统方法
                matched_options = []
                for label, text in options.items():
                    # 检查选项内容是否在答案中
                    if text and (
                        text.strip() in answers
                        or any(
                            keyword in answers
                            for keyword in text.strip().split()
                            if len(keyword) > 1
                        )
                    ):
                        matched_options.append(label)

                if matched_options:
                    result = "".join(sorted(matched_options))
                    logger.success(f"ID:{self.username},多选题匹配成功: {result}")
                    return result

            # 包含匹配：检查答案是否包含在选项中，或选项是否包含在答案中
            for label, text in options.items():
                if text and (text.strip() in answers or answers.strip() in text):
                    logger.success(
                        f"ID:{self.username},答案与选项内容包含匹配: {label}"
                    )
                    return label

            # 增强型内容匹配：去除标点符号后比较
            cleaned_answers = re.sub(r"[^\w\s]", "", answers).lower()
            best_match = None
            best_match_ratio = 0

            for label, text in options.items():
                if not text:
                    continue
                cleaned_text = re.sub(r"[^\w\s]", "", text).lower()

                # 计算两个方向的包含关系
                if cleaned_text in cleaned_answers:
                    ratio = len(cleaned_text) / len(cleaned_answers)
                    if ratio > best_match_ratio:
                        best_match_ratio = ratio
                        best_match = label
                elif cleaned_answers in cleaned_text:
                    ratio = len(cleaned_answers) / len(cleaned_text)
                    if ratio > best_match_ratio:
                        best_match_ratio = ratio
                        best_match = label

            if best_match and best_match_ratio > 0.6:  # 设置较高阈值确保匹配质量
                logger.success(
                    f"ID:{self.username},增强型内容匹配成功: {best_match}, 匹配度: {best_match_ratio:.2f}"
                )
                return best_match

            # 使用difflib计算相似度
            import difflib

            best_match = None
            best_score = 0

            logger.info(f"ID:{self.username},尝试使用相似度匹配")
            for label, text in options.items():
                # 使用difflib计算相似度
                score = difflib.SequenceMatcher(None, answers, text).ratio()
                logger.debug(f"ID:{self.username},选项 {label} 相似度: {score:.2f}")
                if score > best_score and score >= bidui:
                    best_score = score
                    best_match = label

            if best_match:
                logger.success(
                    f"ID:{self.username},答案与选项 {best_match} 匹配成功，相似度: {best_score:.2f}"
                )
                return best_match

            # 关键词匹配：提取答案和选项中的关键词进行匹配
            answer_keywords = set(re.findall(r"\w+", answers.lower()))
            best_match = None
            best_score = 0

            logger.info(
                f"ID:{self.username},尝试使用关键词匹配，答案关键词: {answer_keywords}"
            )
            for label, text in options.items():
                if not text:
                    continue

                text_keywords = set(re.findall(r"\w+", text.lower()))
                logger.debug(f"ID:{self.username},选项 {label} 关键词: {text_keywords}")

                if text_keywords:
                    # 计算关键词匹配度
                    common_keywords = answer_keywords.intersection(text_keywords)
                    logger.debug(
                        f"ID:{self.username},选项 {label} 共同关键词: {common_keywords}"
                    )

                    # 计算两种匹配度：答案关键词在选项中的占比，以及选项关键词在答案中的占比
                    if len(answer_keywords) > 0:
                        answer_match_ratio = len(common_keywords) / len(answer_keywords)
                    else:
                        answer_match_ratio = 0

                    if len(text_keywords) > 0:
                        option_match_ratio = len(common_keywords) / len(text_keywords)
                    else:
                        option_match_ratio = 0

                    # 取较高的匹配度
                    score = max(answer_match_ratio, option_match_ratio)
                    logger.debug(
                        f"ID:{self.username},选项 {label} 关键词匹配度: {score:.2f}"
                    )

                    if (
                        score > best_score and score >= 0.35
                    ):  # 降低阈值到35%以提高匹配成功率
                        best_score = score
                        best_match = label

            if best_match:
                logger.success(
                    f"ID:{self.username},关键词匹配成功: {best_match}，匹配度: {best_score:.2f}"
                )
                return best_match

            # 部分内容匹配：检查答案中的部分内容是否与选项匹配
            # 将答案分割成短语
            answer_phrases = re.split(r"[,，;；、]", answers)
            logger.info(
                f"ID:{self.username},尝试使用短语匹配，答案短语: {answer_phrases}"
            )

            for phrase in answer_phrases:
                phrase = phrase.strip()
                if len(phrase) < 3:  # 忽略过短的短语
                    continue

                for label, text in options.items():
                    if phrase in text or text in phrase:
                        logger.success(
                            f"ID:{self.username},答案短语与选项部分匹配成功: {label}, 短语: {phrase}"
                        )
                        return label

            # 特殊情况处理：检查是否有特定关键词完全匹配
            # 例如题库返回"水分含量小于2%"，选项是"B水分含量小于2％"
            for label, text in options.items():
                # 移除选项中的选项标记(如"A"、"B"等)
                clean_text = re.sub(r"^[A-Z]", "", text).strip()

                # 标准化百分号
                std_answer = answers.replace("%", "％").replace("％", "%")
                std_text = clean_text.replace("%", "％").replace("％", "%")

                if std_answer == std_text:
                    logger.success(f"ID:{self.username},标准化后完全匹配成功: {label}")
                    return label

                # 特殊情况：数字匹配
                answer_numbers = re.findall(r"\d+(?:\.\d+)?%?", answers)
                text_numbers = re.findall(r"\d+(?:\.\d+)?%?", text)

                if (
                    answer_numbers
                    and text_numbers
                    and set(answer_numbers) == set(text_numbers)
                ):
                    # 如果数字完全匹配，并且文本中包含关键词
                    key_terms = [
                        "水分",
                        "含量",
                        "小于",
                        "大于",
                        "等于",
                        "不超过",
                        "不低于",
                    ]
                    if any(term in answers and term in text for term in key_terms):
                        logger.success(
                            f"ID:{self.username},数字和关键词匹配成功: {label}"
                        )
                        return label

            # 特殊情况：对于"水分含量小于2%"这样的答案，尝试特殊匹配
            if "水分含量" in answers and "2%" in answers:
                for label, text in options.items():
                    if "水分含量" in text and "2%" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 水分含量小于2%"
                        )
                        return label
                    elif "水分" in text and "2%" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 水分和2%"
                        )
                        return label

            # 特殊情况：对于"水浴加热"这样的答案，尝试特殊匹配
            if "水浴加热" in answers:
                for label, text in options.items():
                    if "水浴" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 水浴"
                        )
                        return label
                    elif "水" in text and "加热" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 水和加热"
                        )
                        return label

            # 特殊情况：对于"经低温脱水干燥"这样的答案，尝试特殊匹配
            if "经低温脱水干燥" in answers or "脱水干燥" in answers:
                for label, text in options.items():
                    if "干燥" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 干燥"
                        )
                        return label
                    elif "脱水" in text:
                        logger.success(
                            f"ID:{self.username},特殊匹配成功: {label}, 匹配关键词: 脱水"
                        )
                        return label

            # 最后尝试：使用match_answer函数（从API.Xuan导入）
            try:
                from API.Xuan import match_answer

                matched_option = match_answer(answers, options, self.qtp, self.username)
                if matched_option:
                    logger.success(
                        f"ID:{self.username},使用match_answer函数匹配成功: {matched_option}"
                    )
                    return matched_option
            except Exception as e:
                logger.error(
                    f"ID:{self.username},使用match_answer函数匹配失败: {str(e)}"
                )

            logger.warning(f"ID:{self.username},答案无法与任何选项匹配")
            return None
        except Exception as e:
            logger.error(f"ID:{self.username},选项匹配异常: {str(e)}")
            traceback.print_exc()  # 打印详细错误信息
            return None

    def __paixun(self, daan):
        # 使用增强版的排序函数
        return sort_answers(daan)

    def PostDo(self):
        data = {
            "pyFlag": "",
            "courseId": self.couserid,
            "classId": self.classid,
            "api": "1",
            "mooc": "0",
            "workAnswerId": self.workAnswerId,
            "totalQuestionNum": self.totalQuestionNum,
            "fullScore": "100.0",
            "knowledgeid": self.listid,
            "oldSchoolId": "",
            "old": self.old,
            "jobid": self.jobid,
            "workRelationId": self.workRelationId,
            "enc_work": self.enc_work,
            "isphone": "true",
            "userId": self.userid,
            "workTimesEnc": "",
            **self.params,
        }

        # 计算AI答题比例
        ai_ratio = 0
        use_save_mode = False
        ai_status_info = ""

        # 只在特定平台计算AI答题比例
        if (
            hasattr(self.session, "cid")
            and self.session.cid == 9004
            and hasattr(self, "use_ai_answers")
            and self.use_ai_answers
            and self.total_question_count > 0
        ):
            # 确保ai_answer_count不超过total_question_count
            self.ai_answer_count = min(self.ai_answer_count, self.total_question_count)
            ai_ratio = (
                self.ai_answer_count / self.total_question_count
                if self.total_question_count > 0
                else 0
            )

            # 计算题库答题比例
            bank_answer_count = self.total_question_count - self.ai_answer_count
            bank_ratio = (
                bank_answer_count / self.total_question_count
                if self.total_question_count > 0
                else 0
            )

            logger.info(
                f"ID:{self.username},AI答题比例: {ai_ratio:.2%}, AI答题数量: {self.ai_answer_count}, "
                f"题库答题比例: {bank_ratio:.2%}, 题库答题数量: {bank_answer_count}, 总题目数量: {self.total_question_count}"
            )

            # 判断使用哪种提交模式
            # 如果题库答题比例小于80%（即AI答题比例大于20%），使用保存接口而不是提交接口
            use_save_mode = bank_ratio < 0.8  # 等价于 ai_ratio > 0.2

            # 生成状态信息用于进度更新
            if ai_ratio > 0:
                ai_status_info = (
                    f"题库答题比例:{bank_ratio:.0%} | AI答题比例:{ai_ratio:.0%}"
                )
            else:
                ai_status_info = f"题库答题比例:{bank_ratio:.0%}"

            # 保存AI状态信息，供后续使用
            self.session.ai_status_info = ai_status_info

            # 记录使用的模式
            if use_save_mode:
                mode_text = "保存模式"
            else:
                mode_text = "提交模式"
            logger.info(
                f"ID:{self.username},题库答题比例:{bank_ratio:.2%}, 使用{mode_text}"
            )

            if use_save_mode:
                # 更新状态信息
                ai_status_info = f"{ai_status_info}，使用保存模式"
                self.session.ai_status_info = ai_status_info

                try:
                    # 构建保存URL，参考作业保存答案.md
                    url = (
                        "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNewWeb"
                    )

                    # 准备保存数据
                    class_id = self.classid
                    course_id = self.couserid
                    token = self.enc_work
                    total_question_num = self.totalQuestionNum
                    url += f"?_classId={class_id}&courseid={course_id}&token={token}&totalQuestionNum={total_question_num}"
                    url += "&pyFlag=1&ua=pc&formType=post&saveStatus=1&version=1"

                    # 设置请求头
                    headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Accept-Encoding": "gzip, deflate, br, zstd",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache",
                        "X-Requested-With": "XMLHttpRequest",
                        "Origin": "https://mooc1.chaoxing.com",
                        "Sec-Fetch-Site": "same-origin",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Dest": "empty",
                        "Referer": "https://mooc1.chaoxing.com/",
                    }

                    # 发送保存请求
                    response = self.session.post(url, data=data, headers=headers)
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get("status"):
                                logger.success(
                                    f"ID:{self.username},保存答案成功: {response.text}"
                                )
                                # 更新进度信息
                                self._update_progress_info("已保存", ai_status_info)
                            else:
                                logger.warning(
                                    f"ID:{self.username},保存答案失败: {response.text}"
                                )
                        except:
                            logger.warning(
                                f"ID:{self.username},解析保存响应失败: {response.text}"
                            )
                    else:
                        logger.warning(
                            f"ID:{self.username},保存请求失败，状态码: {response.status_code}"
                        )

                    # 使用保存模式后直接返回，不执行后续的提交操作
                    return
                except Exception as e:
                    logger.error(f"ID:{self.username},保存答案异常: {str(e)}")
                    traceback.print_exc()

        # 使用传统方式提交答案
        try:
            # 针对不同平台使用不同的提交方式
            if hasattr(self.session, "cid") and self.session.cid == 9004:
                # 平台ID为9004的提交方式
                submit_url = (
                    "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew"
                )
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "X-Requested-With": "XMLHttpRequest",
                    "Origin": "https://mooc1.chaoxing.com",
                    "Referer": "https://mooc1.chaoxing.com/",
                }
                r = self.session.post(submit_url, data=data, headers=headers)
            else:
                # 其他平台的原有提交方式
                r = self.session.post(
                    "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew",
                    data=data,
                )

            logger.success(f"ID:{self.username},提交答案成功: {r.text}")

            # 更新进度信息
            status = "已提交"
            if hasattr(self.session, "cid") and self.session.cid == 9004:
                status_info = ai_status_info if ai_status_info else "题库答题比例:100%"
            else:
                status_info = "已完成"

            self._update_progress_info(status, status_info)

        except Exception as e:
            logger.error(f"ID:{self.username},提交答案异常: {str(e)}")
            traceback.print_exc()

    def _update_progress_info(self, status, remarks=""):
        """更新进度信息到数据库

        Args:
            status: 状态信息，如"已提交"、"已保存"
            remarks: 备注信息，如AI答题比例
        """
        if not hasattr(self.session, "cid") or self.session.cid != 9004:
            # 非9004平台不需要特殊处理
            return

        try:
            # 尝试获取作业标题
            title_elem = self.html.select_one(
                ".mark_title, h3.title, .titTxt, h1, h2.title"
            )
            title = title_elem.text.strip() if title_elem else "未知作业"

            # 添加状态标记到标题
            title = f"{title} ({status})"

            # 使用更安全的导入方式
            import sys
            import importlib.util

            logger.info(f"ID:{self.username},尝试导入data.Porgres模块")

            # 尝试直接导入
            try:
                from data.Porgres import StartProces

                logger.info(f"ID:{self.username},成功导入data.Porgres.StartProces")
            except ImportError as e:
                logger.warning(f"ID:{self.username},直接导入失败: {str(e)}")

                # 尝试使用importlib导入
                try:
                    spec = importlib.util.find_spec("data.Porgres")
                    if spec is None:
                        logger.error(f"ID:{self.username},找不到data.Porgres模块")
                        return
                    else:
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        StartProces = module.StartProces
                        logger.info(
                            f"ID:{self.username},使用importlib成功导入StartProces"
                        )
                except Exception as e2:
                    logger.error(f"ID:{self.username},importlib导入失败: {str(e2)}")
                    return

            # 创建进度对象并更新进度
            logger.info(f"ID:{self.username},创建StartProces对象")
            p = StartProces(
                self.session,
                [
                    {
                        "kcname": "作业任务",
                        "courseid": self.couserid,
                        "clazzid": self.classid,
                        "cpi": self.cpi,
                    }
                ],
            )

            logger.info(f"ID:{self.username},调用get_platform_9004_progress方法")
            progress, db_remarks = p.get_platform_9004_progress(
                homework_title=title, status=status
            )

            # 合并备注信息
            if remarks:
                if db_remarks:
                    db_remarks = f"{db_remarks} | {remarks}"
                else:
                    db_remarks = remarks

            logger.info(f"ID:{self.username},进度更新: {progress}, {db_remarks}")

            # 更新数据库
            from Config.UserSql import OrderProcessorsql

            pool = OrderProcessorsql()
            # 从session中获取oid
            if hasattr(self.session, "oid"):
                logger.info(
                    f"ID:{self.username},更新数据库记录, oid: {self.session.oid}"
                )
                pool.update_order(
                    f"update qingka_wangke_order set process = '{progress}',remarks = '{db_remarks}' where oid = '{self.session.oid}'"
                )
        except Exception as e:
            logger.error(f"ID:{self.username},更新进度信息异常: {str(e)}")
            traceback.print_exc()

    def PostDoChapterTest(self, save_mode=False):
        """专门用于章节测验的提交方法，使用简单的提交逻辑，避免平台ID 9004的复杂处理

        Args:
            save_mode: 是否使用保存模式。True=保存模式，False=提交模式
        """
        # 根据模式设置不同的参数
        if save_mode:
            # 保存模式：完全模拟"章节测验保存答案.py"的请求格式
            py_flag = "1"
            url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew"

            # 构建保存模式的URL参数（使用enc_work作为token）
            url_params = f"?_classId={self.classid}&courseid={self.couserid}&token={self.enc_work}&totalQuestionNum={self.enc_work}&ua=pc&formType=post&saveStatus=1&version=1&tempsave=1"
            full_url = url + url_params

            # 完整的保存模式请求头（基于章节测验保存答案.py）
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "sec-ch-ua-platform": '"Windows"',
                "X-Requested-With": "XMLHttpRequest",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                "sec-ch-ua-mobile": "?0",
                "Origin": "https://mooc1.chaoxing.com",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": f"https://mooc1.chaoxing.com/mooc-ans/work/doHomeWorkNew?courseId={self.couserid}&workId={self.workRelationId}&api=1&knowledgeid={self.listid}&classId={self.classid}&oldWorkId={self.jobid.replace('work-', '')}&jobid={self.jobid}&type=&isphone=false&enc={self.enc_work}&cpi={getattr(self, 'cpi', '')}&mooc2=1&skipHeader=true&originJobId={self.jobid}&fromType=",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            }
        else:
            # 提交模式：使用原项目代码的简单逻辑
            py_flag = ""
            full_url = "https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew"
            headers = {}  # 提交模式使用session的默认请求头

        # 提取oldWorkId（从jobid中去掉"work-"前缀）
        old_work_id = self.jobid.replace("work-", "") if self.jobid.startswith("work-") else self.jobid

        # 构建请求数据
        data = {
            "pyFlag": py_flag,
            "courseId": self.couserid,
            "classId": self.classid,
            "api": "1",
            "workAnswerId": self.workAnswerId,
            "answerId": self.workAnswerId,
            "totalQuestionNum": self.enc_work if save_mode else self.totalQuestionNum,  # 保存模式使用enc_work作为totalQuestionNum
            "fullScore": "100.0",
            "knowledgeid": self.listid,
            "oldSchoolId": "",
            "oldWorkId": old_work_id,
            "jobid": self.jobid,
            "workRelationId": self.workRelationId,
            "enc": "",
            "enc_work": self.enc_work,
            "userId": self.userid,
            "cpi": getattr(self, 'cpi', ''),
            "workTimesEnc": "",
            "randomOptions": "false",
            "isAccessibleCustomFid": "0",
            **self.params,
        }

        # 如果不是保存模式，添加一些提交模式特有的参数
        if not save_mode:
            data["mooc"] = "0"
            data["old"] = self.old
            data["isphone"] = "true"

        try:
            # 根据模式使用不同的URL、请求头和日志
            mode_text = "保存" if save_mode else "提交"

            if save_mode:
                # 保存模式：使用完整的请求头，不依赖session的默认headers
                r = self.session.post(full_url, data=data, headers=headers)
                logger.info(f"ID:{self.username},保存模式请求URL: {full_url}")
            else:
                # 提交模式：使用session的默认请求头
                r = self.session.post(full_url, data=data)

            # 检查响应结果
            if "提交失败，参数异常" in r.text or "保存失败" in r.text or '"status":false' in r.text:
                logger.error(f"ID:{self.username},章节测验{mode_text}失败: {r.text}")
                if save_mode:
                    logger.error(f"ID:{self.username},保存请求URL: {full_url}")
                    logger.error(f"ID:{self.username},保存数据: {data}")
            else:
                logger.success(f"ID:{self.username},章节测验{mode_text}成功: {r.text}")

        except Exception as e:
            logger.error(f"ID:{self.username},章节测验{mode_text}异常: {str(e)}")
            traceback.print_exc()

    def get_ai_answer_for_choice(self, quest, question_text):
        """使用AI为选择题生成答案

        Args:
            quest: 题目的BS4元素
            question_text: 题目文本

        Returns:
            (answer_letter, answer_explanation)
        """
        try:
            # 首先尝试获取题目类型
            question_type = ""
            type_span = quest.select_one(".colorShallow")
            if type_span:
                question_type = type_span.text.strip()

            # 确保题目文本正确保留空格
            # 我们不使用strip_title函数，因为它会移除所有空格
            if not question_text:
                # 如果传入的题目为空，尝试重新提取
                question_div = (
                    quest.select_one("h3.mark_name")
                    or quest.select_one(".Zy_TItle")
                    or quest.select_one("div.Py-m1-title")
                )
                if question_div:
                    question_text = question_div.text.strip()

            # 如果题目文本已经被处理（空格被移除），尝试从原始HTML中提取
            if question_text and " " not in question_text:
                # 从题目元素中重新获取文本，保留原始格式
                question_elem = (
                    quest.select_one("h3")
                    or quest.select_one("div.Py-m1-title")
                    or quest.select_one(".Zy_TItle")
                )
                if question_elem:
                    raw_text = question_elem.text
                    # 只移除题号和题型，保留其他格式
                    if raw_text:
                        # 移除题目序号
                        raw_text = re.sub(r"^\d+\.", "", raw_text)
                        # 移除题型标签，但保留内部空格
                        raw_text = re.sub(r"\([^)]*\)", "", raw_text)
                        raw_text = re.sub(r"（[^）]*）", "", raw_text)
                        question_text = raw_text.strip()

            # 获取所有选项
            options = {}
            option_items = quest.select(".clearfix li") or quest.select(
                ".stem_answer .clearfix"
            )
            if not option_items:
                option_items = quest.select(".answerBg")

            # 如果仍然没有找到选项，尝试更广泛的选择器
            if not option_items:
                option_items = quest.select("[class*='answer']")

            # 收集选项文本
            for i, option in enumerate(option_items):
                option_label = chr(65 + i)  # A, B, C, ...
                option_text = ""

                # 尝试获取完整选项内容
                # 首先获取选项标签(A,B,C,D)
                option_label_elem = (
                    option.select_one("span.fl")
                    or option.select_one(".num_option")
                    or option.select_one("span[data]")
                )

                # 然后获取选项内容，使用最精确的选择器
                option_content_elem = option.select_one(
                    ".answer_p"
                ) or option.select_one("div.fl")

                # 如果找到了内容元素，提取文本，保留原始格式
                if option_content_elem:
                    # 如果有p标签，优先从p标签中提取
                    p_tags = option_content_elem.select("p")
                    if p_tags:
                        option_text = " ".join(p.get_text(strip=False) for p in p_tags)
                    else:
                        option_text = option_content_elem.get_text(strip=False)

                # 如果仍然没找到，提取整个选项的文本，但排除选项标签部分
                if not option_text:
                    full_text = option.get_text(strip=False)
                    if option_label_elem:
                        label_text = option_label_elem.text
                        # 从完整文本中移除选项标签
                        option_text = full_text.replace(label_text, "", 1)
                    else:
                        option_text = full_text

                # 整理选项文本，保留空格，但删除前后多余空白
                option_text = option_text.strip()

                # 如果选项文本以选项标签开头，则移除它
                if option_text.startswith(option_label):
                    option_text = option_text[1:].strip()

                # 确保选项文本有内容
                if not option_text:
                    option_text = f"选项{option_label}"

                # 保存选项
                options[option_label] = option_text

            # 记录提取到的选项
            # logger.debug(f"ID:{self.username},提取到选项: {options}")

            # 如果未找到选项，返回默认值
            if not options:
                logger.warning(f"ID:{self.username},未找到任何选项，返回默认答案")
                return "A", "未找到选项"

            # 构建完整的问题，包含题型和选项
            full_question = f"{question_type} {question_text}\n\n"
            for label, text in options.items():
                full_question += f"{label}. {text}\n"

            # 记录发送给AI的完整问题，仅在DEBUG级别
            if logger.level("DEBUG").no <= logger.level("INFO").no:
                logger.debug(f"ID:{self.username},发送给AI的问题: {full_question}")

            # 调用AI接口
            # AI接口配置
            AI_API_URL = "http://tk.mixuelo.cc/api.php?act=aimodel"
            AI_API_KEY = "zXPX828s29Kk7Yj2"
            AI_MODEL = "deepseek-chat"

            # 准备请求数据 - 修改prompt让AI直接返回答案内容而不是选项字母
            data = {
                "key": AI_API_KEY,
                "model": AI_MODEL,
                "question": full_question,
                "prompt": "你是一个专业的考试助手。请仔细阅读题目和所有选项，然后只回答你认为最正确的选项内容，不要包含选项字母，不要解释原因，只回答选项内容。",
            }

            # 发送请求
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Bearer {AI_API_KEY}",
            }

            response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
            result = response.json()

            # 检查响应
            if result.get("code") == 1:
                # 成功响应，尝试从data或answer字段获取答案
                if "answer" in result:
                    ai_answer = result["answer"].strip()
                elif "data" in result:
                    ai_answer = result["data"].strip()
                else:
                    logger.warning(f"ID:{self.username},AI接口返回格式异常: {result}")
                    return "A", "AI返回格式异常"

                # 仅在非9004平台ID时或DEBUG级别输出AI返回的答案内容
                if (
                    not hasattr(self.session, "cid") or self.session.cid != 9004
                ) or logger.level("DEBUG").no <= logger.level("INFO").no:
                    logger.info(f"ID:{self.username},AI返回的答案内容: {ai_answer}")

                # 预处理AI答案：标准化格式
                # 1. 处理换行符，将其替换为###
                if "\n" in ai_answer and "###" not in ai_answer:
                    logger.info(
                        f"ID:{self.username},检测到换行符分隔的多选题答案，转换为###分隔"
                    )
                    ai_answer = ai_answer.replace("\n", "###")

                # 2. 处理其他常见分隔符
                if "###" not in ai_answer:
                    for sep in [",", "，", ";", "；", "、"]:
                        if sep in ai_answer and len(ai_answer.split(sep)) > 1:
                            logger.info(
                                f"ID:{self.username},检测到'{sep}'分隔的多选题答案，转换为###分隔"
                            )
                            ai_answer = ai_answer.replace(sep, "###")
                            break

                # 首先检查是否直接返回了选项字母
                letter_match = re.match(r"^[A-D]$", ai_answer)
                if letter_match:
                    answer_letter = letter_match.group(0)
                    # 仅在非9004平台ID时输出详细日志
                    if not hasattr(self.session, "cid") or self.session.cid != 9004:
                        logger.info(
                            f"ID:{self.username},AI直接返回选项字母: {answer_letter}"
                        )
                    return answer_letter, f"AI选择: {answer_letter}"

                # 检查是否为多选题答案格式（使用###分隔）
                if "###" in ai_answer:
                    try:
                        # 处理多选题答案
                        answer_parts = ai_answer.split("###")
                        matched_options = []

                        # 清理每个答案部分并与选项匹配
                        for part in answer_parts:
                            part = part.strip()
                            if not part:
                                continue

                            # 尝试匹配每个选项
                            for label, text in options.items():
                                # 完全匹配
                                if text.strip() == part:
                                    matched_options.append(label)
                                    break

                                # 包含匹配
                                elif text.strip() in part or part in text.strip():
                                    matched_options.append(label)
                                    break

                                # 去除标点后比较
                                else:
                                    cleaned_part = re.sub(r"[^\w\s]", "", part).lower()
                                    cleaned_text = re.sub(r"[^\w\s]", "", text).lower()

                                    if (
                                        cleaned_text == cleaned_part
                                        or cleaned_text in cleaned_part
                                        or cleaned_part in cleaned_text
                                    ):
                                        matched_options.append(label)
                                        break

                        # 如果找到匹配的选项，返回排序后的字母组合
                        if matched_options:
                            result = "".join(sorted(set(matched_options)))
                            if (
                                not hasattr(self.session, "cid")
                                or self.session.cid != 9004
                            ):
                                logger.success(
                                    f"ID:{self.username},多选题匹配成功: {result}"
                                )
                            return result, f"AI多选题匹配: {result}"

                    except Exception as e:
                        logger.warning(
                            f"ID:{self.username},处理多选题答案异常: {str(e)}"
                        )

                # 尝试使用Xuan函数进行匹配
                try:
                    matched_option = self.Xuan(quest, ai_answer)
                    if matched_option:
                        # 仅在非9004平台ID时输出详细日志
                        if not hasattr(self.session, "cid") or self.session.cid != 9004:
                            logger.info(
                                f"ID:{self.username},使用Xuan函数匹配成功: {matched_option}"
                            )
                        return matched_option, f"AI内容匹配: {matched_option}"
                except AttributeError:
                    logger.warning(
                        f"ID:{self.username},Xuan方法不可用，使用备选匹配方法"
                    )

                # 如果Xuan函数匹配失败，尝试直接匹配选项内容
                best_match = None
                best_score = 0
                for label, text in options.items():
                    # 使用difflib计算相似度
                    score = difflib.SequenceMatcher(
                        None, ai_answer.lower(), text.lower()
                    ).ratio()
                    if score > best_score:
                        best_score = score
                        best_match = label

                if best_match and best_score > 0.5:  # 设置一个相似度阈值
                    # 仅在非9004平台ID时输出详细日志
                    if not hasattr(self.session, "cid") or self.session.cid != 9004:
                        logger.info(
                            f"ID:{self.username},直接相似度匹配成功: {best_match}, 相似度: {best_score:.2f}"
                        )
                    return best_match, f"AI内容匹配: {best_match}"

                # 如果所有匹配方法都失败，返回默认值
                logger.warning(
                    f"ID:{self.username},AI回答无法匹配任何选项: {ai_answer}"
                )
                return "A", "AI答案无法匹配"
            else:
                error_msg = result.get("msg", "未知错误")
                logger.warning(f"ID:{self.username},AI接口返回错误: {error_msg}")
                return "A", "AI接口错误"
        except Exception as e:
            logger.error(f"ID:{self.username},调用AI生成选择题答案失败: {str(e)}")
            traceback.print_exc()  # 打印详细错误信息
            return "A", "AI调用失败"

    def _generate_ai_answer_for_blank(self, question_text):
        """使用AI为填空题生成答案

        Args:
            question_text: 题目文本

        Returns:
            填空题答案，使用###分隔多个空的答案
        """
        try:
            # 使用AI接口生成答案
            import requests

            # AI接口配置
            AI_API_URL = "http://tk.mixuelo.cc/api.php?act=aimodel"
            AI_API_KEY = "zXPX828s29Kk7Yj2"
            AI_MODEL = "deepseek-chat"

            # 准备请求数据
            data = {
                "key": AI_API_KEY,
                "model": AI_MODEL,
                "question": f"这是一道填空题，请直接给出答案，多个空用###分隔：{question_text}",
                "prompt": "你是一个专业的学习助手，请根据问题提供准确、简洁的答案，不要重复问题内容，直接给出答案。如果是填空题，请将多个空的答案用###分隔。",
            }

            # 发送请求
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Bearer {AI_API_KEY}",
            }

            response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
            result = response.json()

            # 检查响应
            if result.get("code") == 1:
                # 成功响应，尝试从data或answer字段获取答案
                if "answer" in result:
                    ai_answer = result["answer"]
                elif "data" in result:
                    ai_answer = result["data"]
                else:
                    logger.warning(f"ID:{self.username},AI接口返回格式异常: {result}")
                    return None

                # 处理AI回答，确保使用###分隔
                ai_answer = re.sub(r"<.*?>", "", ai_answer)  # 去除HTML标签

                # 检查是否已经包含###分隔符
                if "###" not in ai_answer:
                    # 尝试查找其他分隔符并替换为###
                    for sep in [",", "，", ";", "；", "\n"]:
                        if sep in ai_answer:
                            ai_answer = ai_answer.replace(sep, "###")
                            break

                logger.success(f"ID:{self.username},AI生成填空题答案: {ai_answer}")
                return ai_answer
            else:
                logger.warning(
                    f"ID:{self.username},AI接口返回错误: {result.get('msg', '未知错误')}"
                )
                return None
        except Exception as e:
            logger.error(f"ID:{self.username},生成填空题答案异常: {str(e)}")
            return None

    def _generate_ai_answer_for_judge(self, question_text):
        """使用AI为判断题生成答案

        Args:
            question_text: 题目文本

        Returns:
            判断题答案，"true"或"false"
        """
        try:
            # 使用AI接口生成答案
            import requests

            # AI接口配置
            AI_API_URL = "http://tk.mixuelo.cc/api.php?act=aimodel"
            AI_API_KEY = "zXPX828s29Kk7Yj2"
            AI_MODEL = "deepseek-chat"

            # 准备请求数据
            data = {
                "key": AI_API_KEY,
                "model": AI_MODEL,
                "question": f"这是一道判断题，请判断对错，只回答'对'或'错'：{question_text}",
                "prompt": "你是一个专业的学习助手，请根据问题判断对错，只回答'对'或'错'，不要解释。",
            }

            # 发送请求
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Bearer {AI_API_KEY}",
            }

            response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
            result = response.json()

            # 检查响应
            if result.get("code") == 1:
                # 成功响应，尝试从data或answer字段获取答案
                if "answer" in result:
                    ai_answer = result["answer"]
                elif "data" in result:
                    ai_answer = result["data"]
                else:
                    logger.warning(f"ID:{self.username},AI接口返回格式异常: {result}")
                    return None

                # 处理AI回答，转换为true/false
                ai_answer = re.sub(r"<.*?>", "", ai_answer).strip()  # 去除HTML标签

                # 转换为标准格式
                if (
                    "对" in ai_answer
                    or "正确" in ai_answer
                    or "true" in ai_answer.lower()
                ):
                    return "true"
                else:
                    return "false"
            else:
                logger.warning(
                    f"ID:{self.username},AI接口返回错误: {result.get('msg', '未知错误')}"
                )
                return None
        except Exception as e:
            logger.error(f"ID:{self.username},生成判断题答案异常: {str(e)}")
            return None

    def _generate_ai_answer_for_essay(self, question_text):
        """使用AI为简答题生成答案

        Args:
            question_text: 题目文本

        Returns:
            简答题答案
        """
        try:
            # 使用AI接口生成答案
            import requests

            # AI接口配置
            AI_API_URL = "http://tk.mixuelo.cc/api.php?act=aimodel"
            AI_API_KEY = "zXPX828s29Kk7Yj2"
            AI_MODEL = "deepseek-chat"

            # 准备请求数据
            data = {
                "key": AI_API_KEY,
                "model": AI_MODEL,
                "question": question_text,
                "prompt": "你是一个专业的学习助手，请根据问题提供准确、简洁的答案，不要重复问题内容，直接给出答案。",
            }

            # 发送请求
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Bearer {AI_API_KEY}",
            }

            response = requests.post(AI_API_URL, data=data, headers=headers, timeout=30)
            result = response.json()

            # 检查响应
            if result.get("code") == 1:
                # 成功响应，尝试从data或answer字段获取答案
                if "answer" in result:
                    ai_answer = result["answer"]
                elif "data" in result:
                    ai_answer = result["data"]
                else:
                    logger.warning(f"ID:{self.username},AI接口返回格式异常: {result}")
                    return None

                # 处理AI回答
                ai_answer = re.sub(r"<.*?>", "", ai_answer)  # 去除HTML标签

                logger.success(
                    f"ID:{self.username},AI生成简答题答案: {ai_answer[:50]}..."
                )
                return ai_answer
            else:
                logger.warning(
                    f"ID:{self.username},AI接口返回错误: {result.get('msg', '未知错误')}"
                )
                return None
        except Exception as e:
            logger.error(f"ID:{self.username},生成简答题答案异常: {str(e)}")
            return None
