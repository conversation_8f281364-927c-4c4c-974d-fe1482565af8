#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超星学习通考试自动填充答案脚本
基于章节测验.py架构，用于自动进入考试并填充答案
支持HTTP请求模式和无头浏览器模式两种方式处理滑块验证码

功能特色：
1. 接收params参数（格式：courseId|classId|examId|cpi）
2. 自动拼接完整的考试入口URL
3. 无头浏览器模式自动处理验证码
4. 进入考试界面，自动填充答题
5. 支持多种题型：单选、多选、填空、判断、简答等
6. 智能检测已作答状态，避免重复答题
7. 参考xxt.js油猴脚本的答题逻辑

使用方法：
1. 基本使用（无头模式）：python 考试自动填充答案.py -u 用户名 -p 密码 --params "courseId|classId|examId|cpi"
2. 无头模式（明确指定）：python 考试自动填充答案.py -u 用户名 -p 密码 --params "courseId|classId|examId|cpi" --headless
3. 显示浏览器：python 考试自动填充答案.py -u 用户名 -p 密码 --params "courseId|classId|examId|cpi" --show-browser
4. 启用AI答题：python 考试自动填充答案.py -u 用户名 -p 密码 --params "courseId|classId|examId|cpi" --ai-answer
5. 只保存不提交：python 考试自动填充答案.py -u 用户名 -p 密码 --params "courseId|classId|examId|cpi" --save-only

参数说明：
--params: 考试参数，格式为"courseId|classId|examId|cpi"
--headless: 无头模式运行（不显示浏览器窗口，默认模式）
--show-browser: 显示浏览器窗口（用于调试）
--ai-answer: 启用AI答题功能
--save-only: 只保存答案，不提交考试
--auto-submit: 自动提交考试
--tiku-url: 自定义题库API地址
"""

import sys
import os
import json
import time
import argparse
import re
import requests
import random
from urllib.parse import urlencode

# 导入作业管理器
try:
    from chaoxing_homework_manager import ChaoxingHomeworkManager
    HOMEWORK_MANAGER_AVAILABLE = True
except ImportError:
    print("警告: chaoxing_homework_manager模块未找到，将无法使用登录功能")
    HOMEWORK_MANAGER_AVAILABLE = False

# 导入浏览器操作相关模块
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    print("警告: Selenium未安装，无法使用浏览器自动化功能")
    SELENIUM_AVAILABLE = False

# 导入验证码处理模块
try:
    captcha_module_path = os.path.join(os.path.dirname(__file__), "250403超星学习验证码")
    if captcha_module_path not in sys.path:
        sys.path.insert(0, captcha_module_path)
    from CaptchaHandler import CaptchaHandler
    from ExamCaptchaHandler import ExamCaptchaHandler
    CAPTCHA_HANDLER_AVAILABLE = True
    EXAM_CAPTCHA_HANDLER_AVAILABLE = True
except ImportError:
    print("警告: 验证码处理模块未找到，将无法自动处理滑块验证码")
    CAPTCHA_HANDLER_AVAILABLE = False
    EXAM_CAPTCHA_HANDLER_AVAILABLE = False

# 导入学习通管理器
try:
    from chaoxing_homework_manager import ChaoxingHomeworkManager
except ImportError:
    print("错误: 无法导入ChaoxingHomeworkManager，请确保chaoxing_homework_manager.py文件存在")
    sys.exit(1)

# 默认题库API配置
DEFAULT_TIKU_APIS = [
    "http://tiku6.cc/api.php",
    "http://yx.yunxue.icu/api",
    "http://l2.bb1a.cn:47407/api/v3/tk/answer"
]

# 全局配置
USE_HEADLESS_BROWSER = True  # 是否使用无头浏览器模式
ENABLE_AI_ANSWER = False     # 是否启用AI答题功能
SAVE_ONLY_MODE = False       # 是否只保存答案，不提交考试
ANSWER_INTERVAL = 2.0        # 答题间隔时间（秒）
AUTO_SUBMIT = False          # 是否自动提交考试

class ExamAutoFiller:
    def __init__(self, username, password, params, tiku_url=None, headless=True, auto_submit=False, ai_answer=False, save_only=False, debug_captcha=False):
        """
        初始化考试自动填充器
        :param username: 学习通用户名
        :param password: 学习通密码
        :param params: 考试参数字符串，格式：courseId|classId|examId|cpi
        :param tiku_url: 自定义题库API地址
        :param headless: 是否使用无头浏览器
        :param auto_submit: 是否自动提交考试
        :param ai_answer: 是否启用AI答题
        :param save_only: 是否只保存答案不提交
        :param debug_captcha: 是否启用验证码调试模式（显示浏览器）
        """
        self.username = username
        self.password = password
        self.headless = headless
        self.auto_submit = auto_submit
        self.ai_answer = ai_answer
        self.save_only = save_only
        self.debug_captcha = debug_captcha

        # 解析考试参数 - 修正参数顺序为：courseId|classId|examId|cpi
        try:
            params_list = params.split('|')
            if len(params_list) != 4:
                raise ValueError("参数格式错误，应为：courseId|classId|examId|cpi")
            self.course_id, self.class_id, self.exam_id, self.cpi = params_list
        except Exception as e:
            raise ValueError(f"考试参数解析失败: {e}")

        # 题库API配置
        self.tiku_apis = [tiku_url] if tiku_url else DEFAULT_TIKU_APIS

        # AI答题配置
        self.ai_apis = [
            {
                "url": "http://tk.mixuelo.cc/api.php",
                "key": "zXPX828s29Kk7Yj2",
                "model": "deepseek-chat"
            }
        ]

        # 当前使用的API配置
        self.current_api_index = 0
        self.ai_api_url = self.ai_apis[0]["url"]
        self.ai_api_key = self.ai_apis[0]["key"]
        self.ai_model = self.ai_apis[0]["model"]

        # 初始化浏览器和管理器
        self.driver = None
        self.manager = None

        print(f"考试参数解析成功:")
        print(f"  考试ID: {self.exam_id}")
        print(f"  课程ID: {self.course_id}")
        print(f"  班级ID: {self.class_id}")
        print(f"  个人ID: {self.cpi}")
        print(f"  无头模式: {self.headless}")
        print(f"  AI答题: {self.ai_answer}")
        print(f"  只保存: {self.save_only}")
        print(f"  自动提交: {self.auto_submit}")
        print(f"  验证码调试: {self.debug_captcha}")

    def build_exam_entrance_url(self):
        """
        构建考试入口URL
        根据chaoxing_exam_api_server.py的URL格式构建
        """
        base_url = "https://mooc1.chaoxing.com/exam-ans/exam/test/examcode/examnotes"
        params = {
            "reset": "true",
            "examId": self.exam_id,
            "courseId": self.course_id,
            "classId": self.class_id,
            "cpi": self.cpi
        }

        entrance_url = f"{base_url}?{urlencode(params)}"
        print(f"考试入口URL: {entrance_url}")
        return entrance_url

    def login_and_setup(self):
        """
        登录学习通并设置浏览器
        """
        try:
            # 创建学习通管理器并登录
            print("正在登录学习通...")
            self.manager = ChaoxingHomeworkManager(self.username, self.password)
            
            if not self.manager.login():
                raise Exception("学习通登录失败")
            
            print("学习通登录成功！")
            
            # 设置浏览器选项
            options = Options()

            # 如果启用验证码调试模式，强制使用可视浏览器
            if self.debug_captcha:
                print("启用验证码调试模式，使用可视浏览器...")
                options.add_argument("--start-maximized")
            elif self.headless:
                print("使用无头浏览器模式...")
                options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-gpu")
                options.add_argument("--window-size=1920,1080")
            else:
                print("使用可视浏览器模式...")
                options.add_argument("--start-maximized")
            
            # 通用配置
            options.add_argument("--disable-notifications")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 创建浏览器实例
            self.driver = webdriver.Chrome(options=options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 先访问学习通主页
            self.driver.get("https://mooc1.chaoxing.com")
            
            # 添加cookies
            for cookie in self.manager.session.cookies:
                try:
                    self.driver.add_cookie({
                        'name': cookie.name,
                        'value': cookie.value,
                        'domain': cookie.domain,
                        'path': cookie.path,
                    })
                except:
                    pass
            
            print("浏览器设置完成！")
            return True
            
        except Exception as e:
            print(f"登录和浏览器设置失败: {e}")
            return False

    def enter_exam(self):
        """
        进入考试
        """
        try:
            entrance_url = self.build_exam_entrance_url()
            
            print("正在访问考试入口页面...")
            self.driver.get(entrance_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 处理考试入口页面
            print("处理考试入口页面...")

            # 检查是否需要同意协议
            try:
                agree_btn = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.CLASS_NAME, "face_agreement"))
                )
                print("点击'我已阅读并同意'按钮...")
                agree_btn.click()
                time.sleep(2)
            except TimeoutException:
                print("未找到协议按钮，可能已经同意或不需要同意")

            # 点击进入考试按钮 - 扩展按钮选择器
            start_button_clicked = False
            start_button_selectors = [
                "#startBtn",
                ".startBtn",
                "#start_btn",
                ".start_btn",
                "button[onclick*='goTest']",
                ".btn[onclick*='goTest']",
                "a[onclick*='goTest']",
                ".face_btn",
                ".exam-start-btn",
                "input[value*='进入考试']",
                "button[value*='进入考试']",
                "a[href*='goTest']",
                ".btn-primary",
                ".btn-success"
            ]

            print("尝试点击进入考试按钮...")
            for selector in start_button_selectors:
                try:
                    btn = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"找到进入考试按钮: {selector}")
                    btn.click()
                    start_button_clicked = True
                    time.sleep(3)
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    print(f"点击按钮 {selector} 失败: {e}")
                    continue

            if not start_button_clicked:
                print("未找到进入考试按钮，检查页面状态...")
                # 保存当前页面用于调试
                try:
                    with open("entrance_page_debug.html", "w", encoding="utf-8") as f:
                        f.write(self.driver.page_source)
                    print("已保存入口页面源码到 entrance_page_debug.html")
                except:
                    pass

            # 处理可能出现的确认对话框
            confirm_dialog_selectors = [
                "#tabIntoexam2",
                ".confirm-btn",
                ".modal-confirm",
                "button[onclick*='confirm']",
                ".swal2-confirm",
                ".layui-layer-btn0"
            ]

            for selector in confirm_dialog_selectors:
                try:
                    confirm_btn = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    confirm_btn.click()
                    print(f"成功点击确认对话框按钮: {selector}")
                    time.sleep(2)
                    break
                except TimeoutException:
                    continue
                except Exception as e:
                    continue

            # 检查是否需要点击"开始重考"或"确定重考"按钮
            retest_button_selectors = [
                "button:contains('开始重考')",
                "button:contains('确定重考')",
                "a:contains('开始重考')",
                "a:contains('确定重考')",
                ".retest-btn",
                ".start-retest",
                "input[value*='开始重考']",
                "input[value*='确定重考']"
            ]

            print("检查是否需要点击重考按钮...")
            for selector in retest_button_selectors:
                try:
                    # 使用JavaScript查找包含文本的按钮
                    if "contains" in selector:
                        text = selector.split("'")[1]  # 提取文本
                        tag = selector.split(":")[0]   # 提取标签

                        buttons = self.driver.find_elements(By.TAG_NAME, tag)
                        for btn in buttons:
                            if text in btn.text:
                                print(f"找到重考按钮: {text}")
                                btn.click()
                                time.sleep(3)
                                print("已点击重考按钮")
                                break
                    else:
                        btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        print(f"找到重考按钮: {selector}")
                        btn.click()
                        time.sleep(3)
                        print("已点击重考按钮")
                        break
                except:
                    continue

            # 检查是否需要处理验证码（在点击进入考试按钮后）
            if self.handle_exam_captcha_if_needed():
                print("验证码处理完成")
            
            # 等待考试页面加载
            print("等待考试页面加载...")

            # 扩展考试页面元素选择器
            exam_page_selectors = [
                ".examPaper",
                ".exam-paper",
                ".test-paper",
                ".questionLi",
                ".question-item",
                ".mark_table",
                ".exam-content",
                ".test-content"
            ]

            exam_page_found = False
            for selector in exam_page_selectors:
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"成功进入考试页面 (检测到: {selector})")
                    exam_page_found = True
                    break
                except TimeoutException:
                    continue

            if exam_page_found:
                # 返回考试页面的完整URL
                current_url = self.driver.current_url
                print(f"考试页面URL: {current_url}")
                return True
            else:
                print("未能成功进入考试页面，检查当前页面状态...")

                # 截图保存
                try:
                    self.driver.save_screenshot("error_screenshot.png")
                    print("已保存错误页面截图到 error_screenshot.png")
                except:
                    pass

                # 保存页面源码
                try:
                    with open("error_page_browser.html", "w", encoding="utf-8") as f:
                        f.write(self.driver.page_source)
                    print("错误页面源码已保存到 error_page_browser.html")
                except:
                    pass

                # 返回当前页面URL用于调试
                current_url = self.driver.current_url
                print(f"当前页面URL: {current_url}")

                # 检查页面内容
                try:
                    page_text = self.driver.find_element(By.TAG_NAME, "body").text
                    print(f"页面内容关键词检查:")
                    keywords = ["考试", "题目", "question", "单选", "多选", "判断", "填空"]
                    for keyword in keywords:
                        if keyword in page_text:
                            print(f"  - 包含关键词: {keyword}")
                except:
                    pass

                # 检查是否已经在考试相关页面，但没有找到标准元素
                if ("exam" in self.driver.current_url or
                    "test" in self.driver.current_url or
                    "reVersionTestStartNew" in self.driver.current_url or
                    "考试" in self.driver.title):
                    print("✅ 检测到考试相关页面，继续执行考试流程")
                    return True

                print("❌ 未检测到考试页面，可能需要手动处理")
                return False

        except Exception as e:
            print(f"进入考试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def handle_exam_captcha_if_needed(self):
        """
        检查并处理考试页面的验证码
        参考章节测验.py的验证码处理逻辑
        """
        try:
            # 检查是否出现滑块验证码容器
            print("检查是否出现滑块验证码...")

            # 扩展验证码选择器，支持更多验证码类型
            captcha_selectors = [
                ".u-captcha.u-layer",
                "#captcha",
                ".captcha",
                ".verify",
                ".slide-verify",
                ".slider-verify",
                "[id*='captcha']",
                "[class*='captcha']",
                "[class*='verify']",
                ".cx_captcha",
                ".verification"
            ]

            captcha_found = False
            used_selector = None

            for selector in captcha_selectors:
                try:
                    WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    print(f"检测到滑块验证码容器 (选择器: {selector})")
                    captcha_found = True
                    used_selector = selector
                    break
                except TimeoutException:
                    continue

            if captcha_found:
                # 截图保存
                try:
                    self.driver.save_screenshot("captcha_screenshot.png")
                    print("已保存验证码截图到 captcha_screenshot.png")
                except:
                    pass

                # 验证码处理主循环，支持失败后重新开始
                # 增加重试次数，确保3次内验证成功
                max_full_attempts = 8  # 最多重新开始8次，确保有足够的尝试机会
                for full_attempt in range(1, max_full_attempts + 1):
                    print(f"第{full_attempt}轮验证码处理...")

                    # 直接使用浏览器方式处理验证码（移除API方式）
                    if CAPTCHA_HANDLER_AVAILABLE:
                        print("尝试浏览器方式处理验证码...")
                        # 尝试自动处理验证码
                        captcha_success, _, x_distance = self.handle_captcha_automatically()

                        if captcha_success:
                            print(f"识别成功: x = {x_distance}")

                            # 直接在浏览器中执行验证码处理，不需要validate_str
                            browser_success = self.handle_captcha_in_browser(None, x_distance, max_attempts=30)

                            if browser_success:
                                print("验证码处理成功")

                                # 验证验证码是否真正消失
                                if self.verify_captcha_disappeared():
                                    print("验证码已消失，验证成功")

                                    # 等待页面跳转到考试页面
                                    print("等待页面跳转...")
                                    entrance_url = self.build_exam_entrance_url()

                                    for wait_time in range(10):  # 等待最多10秒
                                        time.sleep(1)

                                        # 检查是否跳转到考试页面
                                        try:
                                            # 检查是否有考试相关元素
                                            exam_elements = self.driver.find_elements(By.CSS_SELECTOR, ".examPaper, .questionLi, .exam-container")
                                            if exam_elements:
                                                current_url = self.driver.current_url
                                                print(f"进入考试页面成功")
                                                print(f"考试页面URL: {current_url}")
                                                return True

                                            # 检查URL是否包含考试相关路径
                                            current_url = self.driver.current_url
                                            if "exam" in current_url and "test" in current_url and current_url != entrance_url:
                                                print(f"考试页面URL: {current_url}")
                                                return True

                                        except Exception as e:
                                            pass

                                    print("页面跳转超时，但验证码处理成功")
                                    return True
                                else:
                                    print("验证码仍然存在，继续尝试...")
                                    continue

                        # 如果自动处理失败，尝试重新开始
                        if full_attempt < max_full_attempts:
                            print("自动处理失败，尝试重新开始...")
                            try:
                                # 查找重试按钮
                                retry_elements = self.driver.find_elements(By.CSS_SELECTOR, ".u-captcha-refresh, .refresh-btn")
                                if retry_elements:
                                    retry_elements[0].click()
                                    time.sleep(2)
                                    continue
                            except Exception as e:
                                print(f"重新开始失败: {str(e)[:50]}...")

                    else:
                        print("验证码处理模块不可用，但继续执行")
                        # 强制隐藏验证码窗口
                        try:
                            self.driver.execute_script("""
                                var captchaElements = document.querySelectorAll('#captcha, .captcha, .verify, [id*="captcha"], [class*="captcha"]');
                                captchaElements.forEach(function(el) {
                                    el.style.display = 'none';
                                    el.style.visibility = 'hidden';
                                });
                            """)
                        except:
                            pass
                        return True

                print("验证码处理失败，但继续执行")
                # 强制隐藏验证码窗口
                try:
                    self.driver.execute_script("""
                        var captchaElements = document.querySelectorAll('#captcha, .captcha, .verify, [id*="captcha"], [class*="captcha"]');
                        captchaElements.forEach(function(el) {
                            el.style.display = 'none';
                            el.style.visibility = 'hidden';
                        });
                    """)
                except:
                    pass
                return True
            else:
                print("未检测到滑块验证码")
                return True

        except Exception as e:
            print(f"处理验证码时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def verify_captcha_disappeared(self):
        """
        验证验证码是否已经消失

        Returns:
            bool: True表示验证码已消失，False表示验证码仍然存在
        """
        try:
            # 检查验证码容器是否仍然存在且可见
            captcha_selectors = [
                ".u-captcha.u-layer",
                "#captcha",
                ".captcha",
                ".verify",
                ".slide-verify",
                ".slider-verify",
                "[id*='captcha']",
                "[class*='captcha']",
                "[class*='verify']",
                ".cx_captcha",
                ".verification"
            ]

            for selector in captcha_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        # 检查元素是否可见
                        if element.is_displayed():
                            print(f"验证码容器仍然可见: {selector}")
                            return False
                except:
                    continue

            print("验证码容器已消失或隐藏")
            return True

        except Exception as e:
            print(f"检查验证码状态时出错: {e}")
            # 出错时假设验证码已消失，继续流程
            return True



    def handle_captcha_automatically(self, max_attempts=5):
        """
        自动处理滑块验证码，支持重试机制和智能优化
        参考章节测验.py中的handle_captcha_automatically函数
        """
        try:
            if not CAPTCHA_HANDLER_AVAILABLE:
                print("验证码处理模块不可用，无法自动处理验证码")
                return False, None, None

            print("开始智能处理滑块验证码...")

            # 创建考试专用验证码处理器，使用manager的session和考试入口URL
            if EXAM_CAPTCHA_HANDLER_AVAILABLE:
                exam_url = self.build_exam_entrance_url()
                captcha_handler = ExamCaptchaHandler(session=self.manager.session, exam_url=exam_url)
                print("使用考试专用验证码处理器")
            else:
                # 降级使用通用验证码处理器
                captcha_handler = CaptchaHandler(session=self.manager.session)
                print("使用通用验证码处理器（降级模式）")

            # 记录历史识别结果，用于智能调整
            history_results = []

            # 使用考试专用验证码处理器进行识别
            if EXAM_CAPTCHA_HANDLER_AVAILABLE and isinstance(captcha_handler, ExamCaptchaHandler):
                print("使用考试专用验证码处理器进行滑块识别...")
                print(f"考试captchaId: {captcha_handler.captcha_id}")
                print(f"考试referer: {captcha_handler.referer}")

                # 只进行图片获取和识别，不进行API验证
                for attempt in range(1, max_attempts + 1):
                    print(f"第{attempt}次尝试识别验证码...")

                    # 获取验证码图片并识别
                    shade_image, cutout_image, token = captcha_handler.get_captcha_images()
                    if shade_image is None or cutout_image is None or token is None:
                        print("获取验证码图片失败")
                        if attempt < max_attempts:
                            print("等待2秒后重试...")
                            time.sleep(2)
                            continue
                        return False, None, None

                    # 识别滑动距离
                    x_distance = captcha_handler.recognize_slide_distance(shade_image, cutout_image)
                    if x_distance is None:
                        print("识别滑动距离失败")
                        if attempt < max_attempts:
                            print("等待2秒后重试...")
                            time.sleep(2)
                            continue
                        return False, None, None

                    print(f"识别到滑块位置: x = {x_distance}")
                    # 直接返回识别结果，不进行API验证
                    return True, None, x_distance

            # 降级到原有的逐步处理逻辑（用于通用CaptchaHandler）
            for attempt in range(1, max_attempts + 1):
                print(f"第{attempt}次尝试识别验证码...")

                # 获取验证码图片并识别
                shade_image, cutout_image, token = captcha_handler.get_captcha_images()
                if shade_image is None or cutout_image is None or token is None:
                    print("获取验证码图片失败")
                    if attempt < max_attempts:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue
                    return False, None, None

                # 识别滑动距离
                x_distance = captcha_handler.recognize_slide_distance(shade_image, cutout_image)
                if x_distance is None:
                    print("识别滑动距离失败")
                    if attempt < max_attempts:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue
                    return False, None, None

                # 智能调整识别结果
                adjusted_x = x_distance
                if len(history_results) > 0:
                    avg_x = sum(history_results) / len(history_results)
                    if abs(x_distance - avg_x) > 50:
                        adjusted_x = int((x_distance + avg_x) / 2)
                        print(f"基于历史结果调整: {x_distance} -> {adjusted_x}")

                history_results.append(x_distance)
                print(f"识别到滑块位置: x = {adjusted_x}")

                # 验证验证码
                success, validate_str = captcha_handler.verify_captcha(token, adjusted_x)

                if success:
                    print("滑块验证码智能处理成功！")
                    if validate_str:
                        print(f"获取到validate字符串: {validate_str}")
                    return True, validate_str, adjusted_x
                else:
                    print(f"第{attempt}次验证失败")
                    if attempt < max_attempts:
                        print("等待2秒后重试...")
                        time.sleep(2)
                        continue

            print("滑块验证码智能处理失败，已达到最大重试次数")
            return False, None, None

        except Exception as e:
            print(f"自动处理验证码时出错: {e}")
            import traceback
            traceback.print_exc()
            return False, None, None

    def handle_captcha_in_browser(self, validate_str, x_distance, max_attempts=20):
        """
        在浏览器中处理滑块验证码，支持多种策略和重试
        参考章节测验.py中的handle_captcha_in_browser函数
        """
        try:
            print("在浏览器中处理滑块验证码...")

            # 跳过validate字符串设置（API方式已移除）
            if validate_str:
                try:
                    # 尝试多种可能的元素ID
                    validate_selectors = [
                        "captchavalidate",
                        "validate",
                        "captcha_validate",
                        "captcha-validate"
                    ]

                    validate_set = False
                    for selector in validate_selectors:
                        try:
                            self.driver.execute_script(f"document.getElementById('{selector}').value = '{validate_str}';")
                            print(f"已设置validate字符串到页面 (使用选择器: {selector})")
                            validate_set = True
                            break
                        except:
                            continue

                    if not validate_set:
                        # 如果没有找到validate输入框，尝试直接设置到window对象
                        self.driver.execute_script(f"window.captchaValidate = '{validate_str}';")
                        print("已设置validate字符串到window对象")

                except Exception as e:
                    print(f"设置validate字符串失败: {e}")

            print("开始滑块移动...")

            # 基于52pojie文章和ExamCaptchaHandler优化的移动距离计算策略
            # 52pojie文章指出ddddocr返回的就是正确的目标位置，应该直接使用
            # 考试入口验证码与登录入口可能有细微差异，优先使用精确识别结果
            move_strategies = [
                x_distance,         # 直接使用识别坐标（52pojie推荐方式，最高优先级）
                x_distance - 1,     # 微调-1px（像素级精度）
                x_distance + 1,     # 微调+1px（像素级精度）
                x_distance - 2,     # 微调-2px
                x_distance + 2,     # 微调+2px
                x_distance - 3,     # 微调-3px
                x_distance + 3,     # 微调+3px
                x_distance - 4,     # 微调-4px
                x_distance + 4,     # 微调+4px
                x_distance - 5,     # 微调-5px
                x_distance + 5,     # 微调+5px
                # 减少固定位置尝试，因为考试入口的滑块位置可能与登录入口不同
                # 只在识别结果完全失效时才使用经验位置
                239,                # 经验位置1（用户反馈成功位置）
                235,                # 经验位置2
                243,                # 经验位置3
                230,                # 经验位置4
                245,                # 经验位置5
            ]

            for attempt in range(1, max_attempts + 1):
                print(f"第{attempt}次尝试移动滑块...")

                try:
                    # 检查滑块元素是否存在
                    slider_selectors = [
                        ".cx_rightBtn",
                        ".slider-btn",
                        ".slide-btn",
                        "[class*='slider']",
                        "[class*='slide']"
                    ]

                    slider = None
                    for selector in slider_selectors:
                        sliders = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if sliders:
                            slider = sliders[0]
                            print(f"找到滑块元素 (使用选择器: {selector})")
                            break

                    if not slider:
                        print("滑块元素已消失，验证已完成")
                        return True  # 滑块消失说明验证成功

                    # 使用不同的移动策略
                    move_distance = move_strategies[attempt - 1] if attempt <= len(move_strategies) else x_distance

                    # 检查是否出现"失败过多，点此重试"提示
                    try:
                        retry_tip = self.driver.find_element(By.CSS_SELECTOR, ".cx_fallback_tip")
                        if "失败过多" in retry_tip.text or "点此重试" in retry_tip.text:
                            print("检测到重试提示，点击重试...")
                            retry_tip.click()
                            time.sleep(2)
                            continue
                    except:
                        pass  # 没有重试提示，继续正常流程

                    # 重置滑块位置
                    self.driver.execute_script("""
                        var slider = document.querySelector('.cx_rightBtn');
                        var container = document.querySelector('.cx_hkinnerWrap');
                        var indicator = document.querySelector('.cx_slider_indicator');
                        if (slider) slider.style.left = '0px';
                        if (indicator) indicator.style.width = '0px';
                        if (container) {
                            container.classList.remove('cx_success', 'cx_error');
                        }
                    """)

                    # 执行人性化滑块拖拽（模拟真实用户行为）
                    print(f"开始移动滑块到位置: {move_distance}")

                    # 添加移动策略选择
                    import random
                    strategy = random.choice(['smooth', 'stepped', 'curved'])

                    actions = ActionChains(self.driver)
                    actions.click_and_hold(slider)

                    if strategy == 'smooth':
                        # 平滑移动：一次性移动到目标位置
                        actions.move_by_offset(move_distance, 0)
                    elif strategy == 'stepped':
                        # 分步移动：模拟用户分几步移动
                        steps = random.randint(2, 4)
                        step_size = move_distance / steps
                        for i in range(steps):
                            step_offset = step_size + random.uniform(-2, 2)  # 添加随机性
                            actions.move_by_offset(step_offset, random.uniform(-1, 1))  # 添加轻微的y轴偏移
                            actions.pause(random.uniform(0.1, 0.3))  # 步骤间暂停
                    else:  # curved
                        # 曲线移动：模拟用户不完全直线的移动
                        segments = 5
                        segment_size = move_distance / segments
                        for i in range(segments):
                            x_offset = segment_size + random.uniform(-1, 1)
                            y_offset = random.uniform(-2, 2) if i < segments-1 else 0  # 最后一步回到y=0
                            actions.move_by_offset(x_offset, y_offset)
                            actions.pause(random.uniform(0.05, 0.15))

                    actions.release()
                    actions.perform()
                    print(f"已移动滑块到位置: {move_distance} (策略: {strategy})")

                    # 等待验证处理
                    time.sleep(3)

                    # 检查是否验证成功
                    try:
                        # 检查多种可能的成功标识
                        success_selectors = [
                            ".cx_success",
                            ".success",
                            ".captcha-success",
                            "[class*='success']"
                        ]

                        success_found = False
                        for selector in success_selectors:
                            success_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            if success_elements:
                                print(f"第{attempt}次移动验证成功 (检测到: {selector})")
                                success_found = True
                                break

                        if success_found:
                            # 隐藏验证码窗口并触发可能的回调事件
                            try:
                                self.driver.execute_script("""
                                    var captchaDiv = document.getElementById('captcha');
                                    if (captchaDiv) {
                                        captchaDiv.style.display = 'none';
                                    }

                                    // 尝试触发验证成功的回调事件
                                    if (typeof window.captchaSuccess === 'function') {
                                        window.captchaSuccess();
                                    }

                                    // 尝试触发可能的表单提交或页面跳转
                                    var forms = document.querySelectorAll('form');
                                    forms.forEach(function(form) {
                                        if (form.onsubmit) {
                                            form.onsubmit();
                                        }
                                    });
                                """)
                            except Exception as e:
                                print(f"执行验证成功脚本时出错: {e}")

                            return True
                        else:
                            print(f"第{attempt}次移动验证失败")

                    except Exception as e:
                        print(f"第{attempt}次移动验证失败: {e}")

                        # 检查是否有重试提示
                        try:
                            retry_tip = self.driver.find_element(By.CSS_SELECTOR, ".u-captcha-tip")
                            if "失败过多" in retry_tip.text or "点此重试" in retry_tip.text:
                                print("检测到重试提示，重新开始...")
                                retry_tip.click()
                                time.sleep(3)
                                # 重置尝试计数，重新开始
                                return self.handle_captcha_in_browser(validate_str, x_distance, max_attempts)
                        except:
                            pass  # 没有重试提示

                        if attempt < max_attempts:
                            time.sleep(2)

                except Exception as e:
                    print(f"第{attempt}次移动滑块时出错: {e}")
                    if attempt < max_attempts:
                        time.sleep(2)

            print("滑块验证码处理失败，但继续执行")
            return True  # 即使处理失败也继续，避免阻塞流程

        except Exception as e:
            print(f"在浏览器中处理验证码时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def handle_captcha_automatically_in_browser(self):
        """
        在浏览器中自动处理滑块验证码
        参考章节测验.py中的handle_captcha_in_browser函数
        """
        try:
            print("自动处理滑块验证码...")

            # 简化的验证码处理逻辑
            slider = self.driver.find_element(By.CLASS_NAME, "cx_rightBtn")
            actions = ActionChains(self.driver)
            actions.click_and_hold(slider)

            # 使用多种移动策略
            import random
            move_distance = random.randint(180, 220)  # 随机移动距离
            strategy = random.choice(['smooth', 'stepped', 'curved'])

            if strategy == 'smooth':
                actions.move_by_offset(move_distance, 0)
            elif strategy == 'stepped':
                steps = random.randint(3, 5)
                step_size = move_distance / steps
                for i in range(steps):
                    step_offset = step_size + random.uniform(-2, 2)
                    actions.move_by_offset(step_offset, random.uniform(-1, 1))
                    actions.pause(random.uniform(0.1, 0.3))
            else:  # curved
                segments = 5
                segment_size = move_distance / segments
                for i in range(segments):
                    x_offset = segment_size + random.uniform(-1, 1)
                    y_offset = random.uniform(-2, 2) if i < segments-1 else 0
                    actions.move_by_offset(x_offset, y_offset)
                    actions.pause(random.uniform(0.05, 0.15))

            actions.release()
            actions.perform()

            time.sleep(3)

            # 检查是否验证成功
            try:
                # 检查多种可能的成功标识
                success_selectors = [
                    ".cx_success",
                    ".success",
                    ".captcha-success",
                    "[class*='success']"
                ]

                for selector in success_selectors:
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if success_elements:
                        print(f"验证码处理成功 (检测到: {selector})")
                        return True

                print("验证码处理失败，尝试强制设置成功状态")
                # 强制设置验证成功状态
                try:
                    self.driver.execute_script("""
                        var container = document.querySelector('.cx_hkinnerWrap');
                        if (container) {
                            container.classList.add('cx_success');
                            container.classList.remove('cx_error');
                        }
                        var captchaDiv = document.getElementById('captcha');
                        if (captchaDiv) {
                            captchaDiv.style.display = 'none';
                        }

                        // 尝试触发验证成功事件
                        if (typeof window.captchaSuccess === 'function') {
                            window.captchaSuccess();
                        }
                    """)
                    print("已强制设置验证成功状态")
                except Exception as e:
                    print(f"强制设置验证成功状态失败: {e}")

                return True
            except Exception as e:
                print(f"验证码检查失败: {e}")
                return True  # 即使检查失败也返回True，继续流程

        except Exception as e:
            print(f"自动处理验证码时出错: {e}")
            return False

    def is_valid_question_element(self, question_element, question_text):
        """
        验证是否为有效的题目元素
        :param question_element: 题目元素
        :param question_text: 题目文本
        :return: 是否为有效题目
        """
        try:
            # 基本长度检查
            if not question_text or len(question_text) < 10:
                return False

            # 排除考试信息页面
            exclude_keywords = [
                "考试名称", "考试时长", "考试时间", "考试说明",
                "我已阅读并同意", "开始重考", "确定重考", "取消",
                "考试须知", "注意事项", "考试规则"
            ]

            for keyword in exclude_keywords:
                if keyword in question_text:
                    print(f"排除考试信息页面，包含关键词: {keyword}")
                    return False

            # 检查是否包含题目特征
            question_indicators = [
                "单选题", "多选题", "判断题", "填空题", "简答题",
                "A.", "B.", "C.", "D.",
                "正确", "错误", "对", "错",
                "？", "?", "。",
                "选择", "判断", "填空", "简答"
            ]

            has_question_indicator = False
            for indicator in question_indicators:
                if indicator in question_text:
                    has_question_indicator = True
                    break

            # 检查是否有选项或输入框
            has_options = False
            try:
                # 检查选项
                option_selectors = [
                    'input[type="radio"]',
                    'input[type="checkbox"]',
                    'input[type="text"]',
                    'textarea',
                    '.answer_p',
                    '.answerBg li'
                ]

                for selector in option_selectors:
                    options = question_element.find_elements(By.CSS_SELECTOR, selector)
                    if options:
                        has_options = True
                        break
            except:
                pass

            # 必须有题目特征或选项
            if has_question_indicator or has_options:
                print(f"验证通过: 题目特征={has_question_indicator}, 选项={has_options}")
                return True
            else:
                print(f"验证失败: 无题目特征且无选项")
                return False

        except Exception as e:
            print(f"题目验证时出错: {e}")
            return False

    def detect_question_type(self, question_element):
        """
        识别题目类型
        :param question_element: 题目元素
        :return: 题目类型字符串
        """
        try:
            # 检查题目类型标识
            question_html = question_element.get_attribute('outerHTML')
            question_text = question_element.text

            # 通过题目标题判断
            if any(keyword in question_text for keyword in ['单选题', '单项选择', '单选']):
                return "单选题"
            elif any(keyword in question_text for keyword in ['多选题', '多项选择', '多选']):
                return "多选题"
            elif any(keyword in question_text for keyword in ['判断题', '判断']):
                return "判断题"
            elif any(keyword in question_text for keyword in ['填空题', '填空']):
                return "填空题"
            elif any(keyword in question_text for keyword in ['简答题', '简答', '论述题', '论述']):
                return "简答题"

            # 通过HTML元素判断
            # 单选题
            if 'type="radio"' in question_html:
                radio_count = question_html.count('type="radio"')
                if radio_count == 2:
                    return "判断题"  # 只有两个选项通常是判断题
                elif radio_count <= 6:
                    return "单选题"
                else:
                    return "多选题"  # 选项过多可能是多选题

            # 多选题
            if 'type="checkbox"' in question_html:
                return "多选题"

            # 填空题
            if 'textDIV' in question_html or 'divText' in question_html or 'input[type="text"]' in question_html:
                return "填空题"

            # 简答题
            if 'eidtDiv' in question_html or 'subEditor' in question_html or 'textarea' in question_html:
                return "简答题"

            # 默认返回单选题
            return "单选题"

        except Exception as e:
            print(f"识别题目类型失败: {e}")
            return "单选题"

    def get_answer_from_tiku(self, question_text, question_type):
        """
        从题库获取答案
        :param question_text: 题目文本
        :param question_type: 题目类型
        :return: 答案文本
        """
        try:
            # 清理题目文本
            clean_question = re.sub(r'^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*', '', question_text)
            clean_question = re.sub(r'\(\s*\d+\.\d+\s*分\s*\)', '', clean_question)
            clean_question = re.sub(r'^\s*\d+[\.\、\:：]\s*', '', clean_question)
            clean_question = clean_question.strip()

            # 尝试从题库API获取答案
            for api_url in self.tiku_apis:
                try:
                    if "tiku6.cc" in api_url:
                        params = {
                            "act": "tk",
                            "key": "DnDW2hHGcHpiW2WQ",
                            "question": clean_question
                        }
                        response = requests.get(api_url, params=params, timeout=10)
                        result = response.json()
                        if result.get('code') == 1 and result.get('data'):
                            print(f"从题库获取到答案: {result['data']}")
                            return result['data']

                    elif "yunxue.icu" in api_url:
                        params = {
                            "token": "admin",
                            "q": clean_question
                        }
                        response = requests.get(api_url, params=params, timeout=10)
                        result = response.json()
                        if result.get('code') == 1 and result.get('data'):
                            print(f"从备用题库获取到答案: {result['data']}")
                            return result['data']

                except Exception as e:
                    print(f"题库API {api_url} 查询失败: {e}")
                    continue

            # 如果启用AI答题且题库未找到答案
            if self.ai_answer:
                return self.get_ai_answer(clean_question, question_type)

            # 如果都没有找到答案，返回默认答案
            print("未从题库获取到答案，使用默认答案")
            return self.get_default_answer(question_type)

        except Exception as e:
            print(f"获取答案失败: {e}")
            return self.get_default_answer(question_type)

    def get_default_answer(self, question_type):
        """
        获取默认答案
        """
        if question_type == "单选题":
            return "A"
        elif question_type == "多选题":
            return "AB"
        elif question_type == "判断题":
            return "正确"
        elif question_type == "填空题":
            return "答案"
        elif question_type == "简答题":
            return "这是一个简答题的答案。"
        else:
            return "A"

    def get_ai_answer(self, question_text, question_element, question_type):
        """
        使用AI接口获取题目答案
        参考xxt.js中的getAIAnswer函数实现
        """
        try:
            if not self.ai_answer:
                return None

            print("正在使用AI获取答案...")

            # 构建完整的题目内容（包含选项）
            full_question = self.build_question_with_options(question_text, question_element, question_type)

            # 调用AI接口
            ai_response = self.call_ai_api(full_question, question_type)

            if ai_response:
                print(f"AI答案: {ai_response}")
                return ai_response
            else:
                print("AI接口调用失败")
                return None

        except Exception as e:
            print(f"AI答题时出错: {e}")
            return None

    def build_question_with_options(self, question_text, question_element, question_type):
        """
        构建包含选项的完整题目内容
        """
        try:
            full_question = question_text

            if question_type in ['单选题', '多选题', '判断题']:
                # 获取选项
                option_selectors = [
                    '.clearfix.answerBg .fl.answer_p',  # 考试页面主要选择器
                    '.answerBg li',
                    '.answer_p',
                    '.answerList li',
                    '.mark_letter li'
                ]

                options = []
                for selector in option_selectors:
                    option_elements = question_element.find_elements(By.CSS_SELECTOR, selector)
                    if option_elements:
                        for i, option in enumerate(option_elements):
                            option_text = option.text.strip()
                            if option_text:
                                # 提取选项字母和内容
                                if option_text.startswith(('A.', 'B.', 'C.', 'D.', 'E.', 'F.')):
                                    options.append(option_text)
                                else:
                                    # 如果没有字母前缀，添加字母
                                    letter = chr(65 + i)  # A, B, C, D...
                                    options.append(f"{letter}. {option_text}")
                        break

                if options:
                    full_question += "\n选项：\n" + "\n".join(options)

            return full_question

        except Exception as e:
            print(f"构建题目选项时出错: {e}")
            return question_text

    def call_ai_api(self, question, question_type):
        """
        调用AI接口获取答案，支持多个API切换
        """
        # 尝试所有可用的API
        for api_index in range(len(self.ai_apis)):
            try:
                import requests

                # 使用当前API配置
                current_api = self.ai_apis[api_index]
                api_url = current_api["url"]
                api_key = current_api["key"]
                api_model = current_api["model"]

                print(f"尝试API {api_index + 1}: {api_url}")

                # 按照AI答题接口.py的格式构建请求
                url = f"{api_url}?act=aimodel"

                # 按照xxt.js的格式构建数据
                from urllib.parse import urlencode
                data_str = urlencode({
                    'key': api_key,
                    'model': api_model,
                    'question': question
                })

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json',
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }

                print(f"使用密钥: {api_key}")
                print(f"题目: {question[:100]}...")

                # 发送请求
                response = requests.post(url, data=data_str, headers=headers, timeout=30)

                print(f"响应状态码: {response.status_code}")

                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"API响应: {result}")

                        if result.get('code') == 1:
                            # 尝试从不同字段获取答案
                            answer = result.get('answer', '') or result.get('data', '')
                            answer = answer.strip() if answer else ''

                            if answer:
                                print(f"AI返回答案: {answer}")
                                # 更新当前使用的API
                                self.current_api_index = api_index
                                self.ai_api_url = api_url
                                self.ai_api_key = api_key
                                self.ai_model = api_model
                                return answer
                            else:
                                print(f"API {api_index + 1} 返回空答案")
                                continue
                        else:
                            print(f"API {api_index + 1} 返回错误: {result.get('msg', '未知错误')}")
                            continue
                    except Exception as e:
                        print(f"解析API {api_index + 1} 响应JSON失败: {e}")
                        print(f"原始响应: {response.text}")
                        continue
                elif response.status_code == 403:
                    print(f"API {api_index + 1} 访问被拒绝 (403)，可能是密钥无效")
                    continue
                else:
                    print(f"API {api_index + 1} 请求失败: HTTP {response.status_code}")
                    continue

            except Exception as e:
                print(f"调用API {api_index + 1} 时出错: {e}")
                continue

        print("所有AI接口都调用失败")
        return None

    def extract_question_text(self, question_element):
        """
        提取题目文本
        :param question_element: 题目元素
        :return: 题目文本
        """
        try:
            # 尝试多种选择器提取题目
            selectors = [
                ".mark_name",
                ".Py-m1-title",
                ".question-title",
                ".stem_text",
                "h3",
                ".title"
            ]

            question_text = ""
            for selector in selectors:
                try:
                    element = question_element.find_element(By.CSS_SELECTOR, selector)
                    question_text = element.text
                    if question_text.strip():
                        break
                except:
                    continue

            if not question_text:
                question_text = question_element.text

            # 清理题目文本
            question_text = re.sub(r'^\s*[\(（【\[]?\s*(单选题|多选题|判断题|填空题|简答题|论述题|分析题)[\s\.\:：,，]*[\d\.]*分?[\)）\]\】]?\s*', '', question_text)
            question_text = re.sub(r'\(\s*\d+\.\d+\s*分\s*\)', '', question_text)
            question_text = re.sub(r'^\s*\d+[\.\、\:：]\s*', '', question_text)
            question_text = question_text.strip()

            return question_text

        except Exception as e:
            print(f"题目文本提取失败: {e}")
            return ""

    def auto_answer_exam(self):
        """
        自动答题主函数 - 考试模式（逐题处理）
        """
        try:
            print("开始处理考试...")
            print("等待测验框架加载...")

            # 等待页面完全加载
            time.sleep(5)

            # 开始逐题处理模式
            return self.process_current_question()

        except Exception as e:
            print(f"自动答题时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_current_question(self):
        """
        处理当前题目（考试模式）
        """
        try:
            print("=== 开始查找当前题目 ===")

            # 首先输出页面基本信息
            current_url = self.driver.current_url
            page_title = self.driver.title
            print(f"当前页面URL: {current_url}")
            print(f"页面标题: {page_title}")

            # 等待页面加载
            time.sleep(3)

            # 检查页面中是否有题目相关的元素
            print("检查页面结构...")
            try:
                # 检查是否有考试容器
                exam_containers = self.driver.find_elements(By.CSS_SELECTOR, "body")
                if exam_containers:
                    page_text = exam_containers[0].text
                    print(f"页面文本内容（前500字符）: {page_text[:500]}...")
            except:
                pass

            # 扩展的题目选择器，适配新版考试页面
            question_selectors = [
                # 优先使用精确的题目选择器
                ".mark_table .mark_item",
                ".mark_table .whiteDiv",
                ".mark_table .questionLi",
                ".examPaper .questionLi",
                ".questionLi",
                ".TiMu",
                ".question-item",
                ".exam-question",
                ".question",
                ".timu",
                ".exam-content .question",
                ".test-question",
                ".paper-question",
                ".exam-paper .question",
                ".test-paper .question",
                # 通用选择器（最后使用，避免误识别）
                "[class*='question']:not([class*='exam-info']):not([class*='exam-title'])",
                "[class*='timu']:not([class*='exam-info'])",
                "[id*='question']"
            ]

            current_question = None
            used_selector = None

            print("尝试查找题目元素...")
            for selector in question_selectors:
                try:
                    questions = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    print(f"选择器 '{selector}' 找到 {len(questions)} 个元素")

                    if questions:
                        # 检查元素是否包含题目内容
                        for i, question in enumerate(questions):
                            try:
                                question_text = question.text.strip()

                                # 更严格的题目验证
                                if self.is_valid_question_element(question, question_text):
                                    current_question = question
                                    print(f"✅ 找到有效题目 (选择器: {selector}, 索引: {i})")
                                    print(f"题目内容预览: {question_text[:100]}...")
                                    break
                            except:
                                continue

                        if current_question:
                            break
                except Exception as e:
                    print(f"选择器 '{selector}' 查找失败: {e}")
                    continue

            if not current_question:
                print("❌ 未找到当前题目")
                print("尝试检查页面是否包含题目关键词...")

                # 检查页面源码中是否包含题目关键词
                try:
                    page_source = self.driver.page_source
                    keywords = ["题目", "question", "单选题", "多选题", "填空题", "判断题", "简答题"]
                    found_keywords = []
                    for keyword in keywords:
                        if keyword in page_source:
                            found_keywords.append(keyword)

                    if found_keywords:
                        print(f"页面包含题目关键词: {found_keywords}")
                        print("可能需要等待页面完全加载或使用不同的选择器")

                        # 尝试等待并重新查找
                        print("等待5秒后重新尝试...")
                        time.sleep(5)
                        return self.process_current_question()
                    else:
                        print("页面不包含题目关键词，可能已完成所有题目")
                        return self.check_exam_completion()
                except:
                    print("无法检查页面源码")
                    return self.check_exam_completion()

            print(f"✅ 成功找到题目，使用选择器: {used_selector}")

            print("处理当前题目...")

            # 检查题目是否已作答
            if self.is_question_answered(current_question):
                print("当前题目已作答，跳转到下一题")
                return self.go_to_next_question()

            # 获取题目类型和内容
            question_type = self.detect_question_type(current_question)
            question_text = self.extract_question_text(current_question)

            print(f"题目类型: {question_type}")
            print(f"题目内容: {question_text[:100]}...")

            # 根据题目类型进行答题
            success = False
            if question_type in ['单选题', '多选题']:
                success = self.answer_choice_question(current_question, question_type, question_text)
            elif question_type == '填空题':
                success = self.answer_fill_question(current_question, question_text)
            elif question_type == '判断题':
                success = self.answer_judge_question(current_question, question_text)
            elif question_type == '简答题':
                success = self.answer_essay_question(current_question, question_text)
            else:
                print(f"未知题目类型: {question_type}")
                success = True  # 跳过未知类型

            if success:
                print("答题完成，准备跳转到下一题")
                # 答题间隔
                time.sleep(random.uniform(2, 4))
                # 跳转到下一题
                return self.go_to_next_question()
            else:
                print("答题失败")
                return False

        except Exception as e:
            print(f"处理当前题目时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def is_question_answered(self, question_element):
        """
        检查题目是否已作答
        """
        try:
            # 检查单选/多选题的选中状态
            selected_inputs = question_element.find_elements(By.CSS_SELECTOR, 'input[type="radio"]:checked, input[type="checkbox"]:checked')
            if selected_inputs:
                return True

            # 检查CSS类标记
            selected_spans = question_element.find_elements(By.CSS_SELECTOR, '.check_answer, .check_answer_dx, .chosen, .selected')
            if selected_spans:
                return True

            # 检查文本输入框和文本域的值
            text_inputs = question_element.find_elements(By.CSS_SELECTOR, 'input[type="text"], textarea')
            for text_input in text_inputs:
                if text_input.get_attribute('value') and text_input.get_attribute('value').strip():
                    return True

            return False
        except Exception as e:
            print(f"检查题目作答状态时出错: {e}")
            return False

    def answer_choice_question(self, question_element, question_type, question_text=None):
        """
        回答选择题（单选/多选）
        """
        try:
            print(f"开始回答{question_type}...")

            # 获取所有选项
            option_selectors = [
                '.clearfix.answerBg .fl.answer_p',  # 考试页面主要选择器
                '.answerBg li',
                '.answer_p',
                '.answerList li',
                '.mark_letter li'
            ]

            options = []
            for selector in option_selectors:
                options = question_element.find_elements(By.CSS_SELECTOR, selector)
                if options:
                    print(f"找到 {len(options)} 个选项 (使用选择器: {selector})")
                    break

            if not options:
                print("未找到选项")
                return False

            # 尝试使用AI答题
            ai_answer = None
            if self.ai_answer and question_text:
                ai_answer = self.get_ai_answer(question_text, question_element, question_type)

            if ai_answer:
                # 使用AI答案进行选项匹配
                return self.match_and_click_options(options, ai_answer, question_type)
            else:
                # 回退到随机策略
                print("使用随机答题策略")
                return self.random_answer_choice(options, question_type)

        except Exception as e:
            print(f"回答选择题时出错: {e}")
            return False

    def match_and_click_options(self, options, ai_answer, question_type):
        """
        根据AI答案匹配并点击选项
        """
        try:
            ai_answer = ai_answer.upper().strip()

            if question_type == '单选题':
                # 单选题：匹配单个选项
                print(f"单选题AI答案: {ai_answer}")

                # 首先尝试字母匹配（如果AI返回A、B、C、D）
                if len(ai_answer) == 1 and ai_answer.upper() in 'ABCD':
                    for option in options:
                        option_text = option.text.strip()
                        if option_text.startswith(ai_answer.upper() + '.') or option_text.startswith(ai_answer.upper() + '、'):
                            if self.click_option(option):
                                print(f"已选择AI推荐选项: {option_text[:50]}...")
                                return True

                # 内容匹配（AI返回选项内容）
                for option in options:
                    option_text = option.text.strip()
                    # 移除选项字母前缀（A. B. C. D.）
                    clean_option_text = option_text
                    if option_text and len(option_text) > 2 and option_text[1] in '.、':
                        clean_option_text = option_text[2:].strip()

                    # 检查AI答案是否与选项内容匹配
                    if (ai_answer in clean_option_text or
                        clean_option_text in ai_answer or
                        ai_answer in option_text or
                        option_text in ai_answer):

                        if self.click_option(option):
                            print(f"已选择AI推荐选项: {option_text[:50]}...")
                            return True

                # 如果没有匹配，尝试模糊匹配
                for option in options:
                    option_text = option.text.strip()
                    if ai_answer.upper() in option_text.upper():
                        if self.click_option(option):
                            print(f"已选择模糊匹配选项: {option_text[:50]}...")
                            return True

            else:  # 多选题
                # 多选题：处理###分隔的答案内容
                selected_count = 0

                # 解析AI答案，支持###分隔的多个答案
                if '###' in ai_answer:
                    ai_answers = [ans.strip() for ans in ai_answer.split('###')]
                else:
                    ai_answers = [ai_answer.strip()]

                print(f"多选题AI答案: {ai_answers}")

                # 遍历每个AI答案，与选项进行内容匹配
                for ai_ans in ai_answers:
                    if not ai_ans:
                        continue

                    for option in options:
                        option_text = option.text.strip()
                        # 移除选项字母前缀（A. B. C. D.）
                        clean_option_text = option_text
                        if option_text and len(option_text) > 2 and option_text[1] in '.、':
                            clean_option_text = option_text[2:].strip()

                        # 检查AI答案是否与选项内容匹配
                        if (ai_ans in clean_option_text or
                            clean_option_text in ai_ans or
                            ai_ans in option_text or
                            option_text in ai_ans):

                            if self.click_option(option):
                                print(f"已选择AI推荐选项: {option_text[:50]}...")
                                selected_count += 1
                            break

                if selected_count > 0:
                    print(f"已选择 {selected_count} 个AI推荐选项")
                    return True

            # 如果AI答案匹配失败，使用随机策略
            print("AI答案匹配失败，使用随机策略")
            return self.random_answer_choice(options, question_type)

        except Exception as e:
            print(f"匹配AI答案时出错: {e}")
            return self.random_answer_choice(options, question_type)

    def random_answer_choice(self, options, question_type):
        """
        随机答题策略（备用方案）
        """
        try:
            if question_type == '单选题':
                # 随机选择一个选项
                selected_option = random.choice(options)
                if self.click_option(selected_option):
                    print("已随机选择选项")
                    return True
                else:
                    return False
            else:  # 多选题
                # 随机选择1-3个选项
                num_to_select = random.randint(1, min(3, len(options)))
                selected_options = random.sample(options, num_to_select)
                success_count = 0
                for option in selected_options:
                    if self.click_option(option):
                        success_count += 1
                print(f"已随机选择 {success_count}/{num_to_select} 个选项")
                return success_count > 0

        except Exception as e:
            print(f"随机答题时出错: {e}")
            return False

    def click_option(self, option_element):
        """
        点击选项
        """
        try:
            # 先处理可能的遮挡层
            self.remove_overlay_elements()

            # 尝试点击选项中的span或input元素
            clickable_elements = option_element.find_elements(By.CSS_SELECTOR, 'span, input')
            if clickable_elements:
                # 使用JavaScript点击，避免被遮挡
                self.driver.execute_script("arguments[0].click();", clickable_elements[0])
            else:
                # 使用JavaScript点击，避免被遮挡
                self.driver.execute_script("arguments[0].click();", option_element)

            # 等待一下确保点击生效
            time.sleep(0.5)
            return True
        except Exception as e:
            print(f"点击选项时出错: {e}")
            # 尝试备用点击方式
            try:
                self.driver.execute_script("arguments[0].click();", option_element)
                time.sleep(0.5)
                return True
            except Exception as e2:
                print(f"备用点击方式也失败: {e2}")
                return False

    def remove_overlay_elements(self):
        """
        移除可能遮挡点击的元素
        """
        try:
            # 移除常见的遮挡层
            overlay_selectors = [
                "#multiTerminalWin",
                ".maskDiv",
                ".mask",
                ".overlay",
                "[style*='z-index: 1000']"
            ]

            for selector in overlay_selectors:
                try:
                    self.driver.execute_script(f"""
                        var elements = document.querySelectorAll('{selector}');
                        elements.forEach(function(el) {{
                            el.style.display = 'none';
                            el.style.visibility = 'hidden';
                            el.style.zIndex = '-1';
                        }});
                    """)
                except:
                    continue

        except Exception as e:
            print(f"移除遮挡层时出错: {e}")

    def answer_fill_question(self, question_element, question_text=None):
        """
        回答填空题 - 支持UEditor富文本编辑器
        """
        try:
            print("开始回答填空题...")

            # 尝试使用AI答题
            ai_answer = None
            if self.ai_answer and question_text:
                ai_answer = self.get_ai_answer(question_text, question_element, '填空题')

            # 处理答案格式
            if ai_answer:
                # 处理多个空的情况，支持###和|分隔
                if '###' in ai_answer:
                    answers = [ans.strip() for ans in ai_answer.split('###')]
                elif '|' in ai_answer:
                    answers = [ans.strip() for ans in ai_answer.split('|')]
                else:
                    answers = [ai_answer.strip()]
                print(f"填空题AI答案: {answers}")
            else:
                answers = ["答案"]  # 默认答案
                print("使用默认填空答案")

            # 查找填空输入框，支持多种选择器
            input_selectors = [
                '.stem_answer .Answer .divText .textDIV textarea',  # 标准UEditor选择器
                '.stem_answer textarea',
                '.mark_answer textarea',
                'textarea',
                'input[type="text"]',
                '.blank-input',
                '.fillblank',
                '[class*="blank"]'
            ]

            inputs = []
            used_selector = None
            for selector in input_selectors:
                inputs = question_element.find_elements(By.CSS_SELECTOR, selector)
                if inputs:
                    used_selector = selector
                    print(f"找到 {len(inputs)} 个填空框 (使用选择器: {selector})")
                    break

            if not inputs:
                print("未找到填空输入框")
                return False

            success_count = 0

            # 遍历每个输入框
            for i, input_elem in enumerate(inputs):
                try:
                    fill_answer = answers[i] if i < len(answers) else answers[0]

                    # 获取元素信息
                    element_id = input_elem.get_attribute('id')
                    element_name = input_elem.get_attribute('name')
                    element_tag = input_elem.tag_name
                    element_class = input_elem.get_attribute('class')

                    print(f"=== 处理第{i+1}个空 ===")
                    print(f"元素标签: {element_tag}")
                    print(f"元素ID: {element_id}")
                    print(f"元素Name: {element_name}")
                    print(f"元素Class: {element_class}")
                    print(f"要填入的答案: {fill_answer}")

                    # 检查UEditor是否存在
                    ueditor_exists = False
                    if element_id:
                        ueditor_exists = self.check_ueditor_exists(element_id)
                        print(f"UEditor检测结果: {ueditor_exists}")

                    # 尝试使用UEditor API填写
                    if element_id and ueditor_exists:
                        print("尝试使用UEditor API填写...")
                        ueditor_success = self.fill_ueditor_content(element_id, fill_answer)
                        if ueditor_success:
                            success_count += 1
                            print(f"✅ UEditor成功填入: {fill_answer}")
                            continue
                        else:
                            print("❌ UEditor填写失败，尝试其他方法")

                    # 如果UEditor失败，尝试直接操作元素
                    print("尝试使用JavaScript直接操作...")
                    try:
                        # 先移除遮挡层
                        self.remove_overlay_elements()

                        # 使用JavaScript设置值
                        self.driver.execute_script("""
                            console.log('开始JavaScript填写:', arguments[1]);

                            // 尝试多种设置方式
                            arguments[0].value = arguments[1];
                            arguments[0].innerHTML = arguments[1];
                            arguments[0].textContent = arguments[1];

                            // 如果是textarea，设置内容
                            if (arguments[0].tagName.toLowerCase() === 'textarea') {
                                arguments[0].value = arguments[1];
                            }

                            // 触发事件
                            var inputEvent = new Event('input', { bubbles: true });
                            arguments[0].dispatchEvent(inputEvent);

                            var changeEvent = new Event('change', { bubbles: true });
                            arguments[0].dispatchEvent(changeEvent);

                            var focusEvent = new Event('focus', { bubbles: true });
                            arguments[0].dispatchEvent(focusEvent);

                            var blurEvent = new Event('blur', { bubbles: true });
                            arguments[0].dispatchEvent(blurEvent);

                            console.log('JavaScript填写完成');
                        """, input_elem, fill_answer)

                        success_count += 1
                        print(f"✅ JavaScript成功填入: {fill_answer}")

                    except Exception as e:
                        print(f"❌ JavaScript填写失败: {e}")

                        # 最后尝试Selenium原生方法
                        print("尝试使用Selenium原生方法...")
                        try:
                            input_elem.clear()
                            input_elem.send_keys(fill_answer)
                            success_count += 1
                            print(f"✅ Selenium成功填入: {fill_answer}")
                        except Exception as e2:
                            print(f"❌ Selenium填写失败: {e2}")

                except Exception as e:
                    print(f"❌ 处理第{i+1}个空时出错: {e}")
                    import traceback
                    traceback.print_exc()

                # 添加延时
                time.sleep(1)

            print(f"已填写 {success_count}/{len(inputs)} 个空")
            return success_count > 0

        except Exception as e:
            print(f"回答填空题时出错: {e}")
            return False

    def check_ueditor_exists(self, element_id):
        """
        检查UEditor是否存在并可用
        """
        try:
            script = f"""
                try {{
                    console.log('检查UEditor，元素ID:', '{element_id}');

                    // 检查UE对象是否存在
                    if (typeof UE === 'undefined') {{
                        console.log('UE对象不存在');
                        return false;
                    }}

                    // 检查getEditor方法是否存在
                    if (!UE.getEditor) {{
                        console.log('UE.getEditor方法不存在');
                        return false;
                    }}

                    // 尝试获取编辑器实例
                    var editor = UE.getEditor('{element_id}');
                    if (!editor) {{
                        console.log('无法获取编辑器实例');
                        return false;
                    }}

                    // 检查编辑器是否已准备好
                    if (!editor.setContent) {{
                        console.log('编辑器setContent方法不存在');
                        return false;
                    }}

                    console.log('UEditor检查通过');
                    return true;
                }} catch (e) {{
                    console.error('UEditor检查失败:', e);
                    return false;
                }}
            """

            result = self.driver.execute_script(script)
            return result

        except Exception as e:
            print(f"UEditor检查失败: {e}")
            return False

    def fill_ueditor_content(self, element_id, content):
        """
        使用UEditor API填写内容
        """
        try:
            # 转义内容中的特殊字符
            escaped_content = content.replace("'", "\\'").replace('"', '\\"').replace('\n', '\\n')

            # 尝试使用UEditor API
            script = f"""
                try {{
                    console.log('开始UEditor填写，ID:', '{element_id}', '内容:', '{escaped_content}');

                    var editor = UE.getEditor('{element_id}');
                    if (editor && editor.setContent) {{
                        // 设置内容
                        editor.setContent('{escaped_content}');
                        console.log('UEditor内容设置完成');

                        // 触发内容变化事件
                        if (editor.fireEvent) {{
                            editor.fireEvent('contentchange');
                            console.log('触发contentchange事件');
                        }}

                        // 同步内容到表单
                        if (editor.sync) {{
                            editor.sync();
                            console.log('同步内容到表单');
                        }}

                        // 触发其他可能的事件
                        if (editor.fireEvent) {{
                            editor.fireEvent('selectionchange');
                            editor.fireEvent('aftersetcontent');
                        }}

                        console.log('UEditor填写成功');
                        return true;
                    }} else {{
                        console.log('编辑器或setContent方法不存在');
                        return false;
                    }}
                }} catch (e) {{
                    console.error('UEditor操作失败:', e);
                    return false;
                }}
            """

            result = self.driver.execute_script(script)
            print(f"UEditor API调用结果: {result}")
            return result

        except Exception as e:
            print(f"UEditor操作失败: {e}")
            return False

    def answer_judge_question(self, question_element, question_text=None):
        """
        回答判断题
        """
        try:
            print("开始回答判断题...")

            # 获取判断选项（通常是"正确"和"错误"）
            option_selectors = [
                '.clearfix.answerBg .fl.answer_p',
                '.answerBg li',
                '.answer_p'
            ]

            options = []
            for selector in option_selectors:
                options = question_element.find_elements(By.CSS_SELECTOR, selector)
                if options:
                    break

            if not options:
                print("未找到判断选项")
                return False

            # 尝试使用AI答题
            ai_answer = None
            if self.ai_answer and question_text:
                ai_answer = self.get_ai_answer(question_text, question_element, '判断题')

            if ai_answer:
                print(f"判断题AI答案: {ai_answer}")

                # 根据AI答案选择正确或错误
                is_correct = any(keyword in ai_answer for keyword in ['正确', '对', '是', '√', 'T', 'true', 'True', 'TRUE'])

                print(f"AI判断结果: {'正确' if is_correct else '错误'}")

                for option in options:
                    option_text = option.text.strip()
                    # 移除选项字母前缀
                    clean_option_text = option_text
                    if option_text and len(option_text) > 2 and option_text[1] in '.、':
                        clean_option_text = option_text[2:].strip()

                    if is_correct:
                        if any(keyword in clean_option_text for keyword in ['正确', '对', '是', '√', 'T']) or \
                           any(keyword in option_text for keyword in ['正确', '对', '是', '√', 'T']):
                            if self.click_option(option):
                                print(f"已选择AI推荐选项: {option_text}")
                                return True
                    else:
                        if any(keyword in clean_option_text for keyword in ['错误', '错', '否', '×', 'F']) or \
                           any(keyword in option_text for keyword in ['错误', '错', '否', '×', 'F']):
                            if self.click_option(option):
                                print(f"已选择AI推荐选项: {option_text}")
                                return True

            # 回退到随机策略
            print("使用随机判断策略")
            selected_option = random.choice(options)
            if self.click_option(selected_option):
                print("已随机选择判断选项")
                return True
            else:
                return False

        except Exception as e:
            print(f"回答判断题时出错: {e}")
            return False

    def answer_essay_question(self, question_element, question_text=None):
        """
        回答简答题 - 支持UEditor富文本编辑器
        """
        try:
            print("开始回答简答题...")

            # 尝试使用AI答题
            ai_answer = None
            if self.ai_answer and question_text:
                ai_answer = self.get_ai_answer(question_text, question_element, '简答题')

            # 确定答案内容
            if ai_answer:
                answer_content = ai_answer
                print(f"简答题AI答案: {answer_content[:100]}...")
            else:
                answer_content = "这是一个简答题的答案。根据题目要求，我认为这个问题的答案是..."
                print("使用默认简答答案")

            # 查找文本域，支持多种选择器
            textarea_selectors = [
                '.stem_answer .eidtDiv textarea',  # 标准UEditor选择器
                '.stem_answer textarea',
                '.mark_answer textarea',
                '.subEditor textarea',
                'textarea',
                '[class*="editor"]',
                '[id*="editor"]',
                '.edui-editor'
            ]

            textareas = []
            used_selector = None
            for selector in textarea_selectors:
                textareas = question_element.find_elements(By.CSS_SELECTOR, selector)
                if textareas:
                    used_selector = selector
                    print(f"找到 {len(textareas)} 个文本域 (使用选择器: {selector})")
                    break

            if not textareas:
                print("未找到简答题文本域")
                return False

            success_count = 0

            # 遍历每个文本域
            for i, textarea in enumerate(textareas):
                try:
                    # 获取元素ID和name，用于UEditor操作
                    element_id = textarea.get_attribute('id')
                    element_name = textarea.get_attribute('name')

                    print(f"处理第{i+1}个文本域，ID: {element_id}, Name: {element_name}")

                    # 尝试使用UEditor API填写
                    if element_id:
                        ueditor_success = self.fill_ueditor_content(element_id, answer_content)
                        if ueditor_success:
                            success_count += 1
                            print(f"使用UEditor成功填入简答题答案")
                            continue

                    # 如果有name属性，也尝试用name作为ID
                    if element_name:
                        ueditor_success = self.fill_ueditor_content(element_name, answer_content)
                        if ueditor_success:
                            success_count += 1
                            print(f"使用UEditor(name)成功填入简答题答案")
                            continue

                    # 如果UEditor失败，尝试直接操作元素
                    try:
                        # 先移除遮挡层
                        self.remove_overlay_elements()

                        # 使用JavaScript设置值
                        self.driver.execute_script("""
                            arguments[0].value = arguments[1];
                            arguments[0].innerHTML = arguments[1];

                            // 触发事件
                            var event = new Event('input', { bubbles: true });
                            arguments[0].dispatchEvent(event);

                            var changeEvent = new Event('change', { bubbles: true });
                            arguments[0].dispatchEvent(changeEvent);
                        """, textarea, answer_content)

                        success_count += 1
                        print(f"使用JavaScript成功填入简答题答案")

                    except Exception as e:
                        print(f"JavaScript填写第{i+1}个文本域失败: {e}")

                        # 最后尝试Selenium原生方法
                        try:
                            textarea.clear()
                            textarea.send_keys(answer_content)
                            success_count += 1
                            print(f"使用Selenium成功填入简答题答案")
                        except Exception as e2:
                            print(f"Selenium填写第{i+1}个文本域失败: {e2}")

                except Exception as e:
                    print(f"处理第{i+1}个文本域时出错: {e}")

                # 添加延时
                time.sleep(0.5)

            print(f"已填写 {success_count}/{len(textareas)} 个简答题")
            return success_count > 0

        except Exception as e:
            print(f"回答简答题时出错: {e}")
            return False

    def go_to_next_question(self):
        """
        跳转到下一题（考试模式）
        """
        try:
            print("查找下一题按钮...")

            # 查找下一题按钮，使用多种选择器
            next_button_selectors = [
                '.nextDiv a.jb_btn',  # 主要选择器
                'a.jb_btn',           # 通用选择器
                'a[onclick*="getTheNextQuestion"]',  # 根据onclick事件查找
                '.jb_btn_92',         # 根据用户提供的class查找
                'a[href="javascript:;"].jb_btn',  # 更精确的选择器
                '.next-btn',          # 备用选择器
                'button[onclick*="next"]',  # 按钮形式
                'input[value*="下一题"]'     # input形式
            ]

            next_button = None
            for selector in next_button_selectors:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    # 检查按钮文本是否包含"下一题"
                    if "下一题" in button.text or "next" in button.text.lower():
                        next_button = button
                        print(f"找到下一题按钮: {selector}")
                        break
                if next_button:
                    break

            if next_button:
                print("点击下一题按钮...")

                # 先处理可能遮挡的maskDiv元素
                try:
                    # 隐藏所有可能遮挡的maskDiv
                    self.driver.execute_script("""
                        var maskDivs = document.querySelectorAll('.maskDiv');
                        maskDivs.forEach(function(div) {
                            if (div.style.display !== 'none') {
                                div.style.display = 'none';
                            }
                        });
                    """)
                    print("已隐藏遮挡元素")
                except Exception as e:
                    print(f"隐藏遮挡元素失败: {e}")

                # 尝试多种点击方式
                click_success = False

                # 方式1：直接点击
                try:
                    next_button.click()
                    click_success = True
                    print("直接点击成功")
                except Exception as e:
                    print(f"直接点击失败: {e}")

                # 方式2：JavaScript点击
                if not click_success:
                    try:
                        self.driver.execute_script("arguments[0].click();", next_button)
                        click_success = True
                        print("JavaScript点击成功")
                    except Exception as e:
                        print(f"JavaScript点击失败: {e}")

                # 方式3：ActionChains点击
                if not click_success:
                    try:
                        actions = ActionChains(self.driver)
                        actions.move_to_element(next_button).click().perform()
                        click_success = True
                        print("ActionChains点击成功")
                    except Exception as e:
                        print(f"ActionChains点击失败: {e}")

                # 方式4：通过onclick事件触发
                if not click_success:
                    try:
                        onclick_attr = next_button.get_attribute('onclick')
                        if onclick_attr:
                            self.driver.execute_script(onclick_attr)
                            click_success = True
                            print("onclick事件触发成功")
                    except Exception as e:
                        print(f"onclick事件触发失败: {e}")

                if click_success:
                    print("已点击下一题按钮")
                    # 等待页面跳转
                    time.sleep(3)
                    # 继续处理下一题
                    return self.process_current_question()
                else:
                    print("所有点击方式都失败，尝试其他方法")
                    return self.check_exam_completion()
            else:
                print("未找到下一题按钮，可能已到达最后一题")
                # 尝试点击"整卷预览"按钮来保存最后一题的答案
                return self.handle_last_question_and_preview()

        except Exception as e:
            print(f"跳转下一题时出错: {e}")
            import traceback
            traceback.print_exc()
            return self.check_exam_completion()

    def handle_last_question_and_preview(self):
        """
        处理最后一题并点击整卷预览
        """
        try:
            print("处理最后一题，查找整卷预览按钮...")

            # 查找整卷预览按钮
            preview_button_selectors = [
                'a.completeBtn[onclick*="topreview"]',  # 主要选择器
                '.completeBtn',                        # 通用选择器
                'a[onclick*="topreview"]',             # 根据onclick事件查找
                '.sub-button a.completeBtn',           # 更精确的选择器
                'a[href="javascript:;"].completeBtn'   # 备用选择器
            ]

            preview_button = None
            for selector in preview_button_selectors:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    # 检查按钮文本是否包含"整卷预览"
                    if "整卷预览" in button.text or "preview" in button.text.lower():
                        preview_button = button
                        print(f"找到整卷预览按钮: {selector}")
                        break
                if preview_button:
                    break

            if preview_button:
                print("点击整卷预览按钮...")
                preview_button.click()
                print("已点击整卷预览按钮")

                # 等待页面跳转到整卷预览
                time.sleep(5)

                # 处理整卷预览页面
                return self.handle_exam_preview_page()
            else:
                print("未找到整卷预览按钮，尝试检查考试完成状态")
                return self.check_exam_completion()

        except Exception as e:
            print(f"处理最后一题和整卷预览时出错: {e}")
            import traceback
            traceback.print_exc()
            return self.check_exam_completion()

    def handle_exam_preview_page(self):
        """
        处理整卷预览页面，检查并填充遗漏的答案
        """
        try:
            print("进入整卷预览页面，检查遗漏的答案...")

            # 等待页面完全加载
            time.sleep(3)

            # 检查当前URL是否包含预览相关关键词
            current_url = self.driver.current_url
            if "preview" not in current_url.lower():
                print(f"当前URL不是预览页面: {current_url}")
                return self.check_exam_completion()

            print(f"当前在整卷预览页面: {current_url}")

            # 查找所有题目并检查遗漏的答案
            missing_count = self.check_and_fill_missing_answers()

            if missing_count > 0:
                print(f"发现并填充了 {missing_count} 道遗漏的题目")
            else:
                print("所有题目都已作答")

            # 检查是否需要提交考试
            return self.check_exam_completion()

        except Exception as e:
            print(f"处理整卷预览页面时出错: {e}")
            import traceback
            traceback.print_exc()
            return self.check_exam_completion()

    def check_and_fill_missing_answers(self):
        """
        在整卷预览页面检查并填充遗漏的答案

        Returns:
            int: 填充的题目数量
        """
        try:
            print("检查整卷预览页面中的遗漏答案...")

            # 查找所有题目容器
            question_containers = self.driver.find_elements(By.CSS_SELECTOR, '.questionLi')
            print(f"找到 {len(question_containers)} 道题目")

            filled_count = 0

            for i, container in enumerate(question_containers, 1):
                try:
                    # 获取题目ID
                    question_id = container.get_attribute('data')
                    if not question_id:
                        continue

                    print(f"检查第 {i} 题 (ID: {question_id})...")

                    # 检查题目是否已作答
                    if self.is_preview_question_answered(container, question_id):
                        print(f"第 {i} 题已作答，跳过")
                        continue

                    print(f"第 {i} 题未作答，开始填充答案...")

                    # 获取题目类型和内容
                    question_type = self.detect_preview_question_type(container)
                    question_text = self.extract_preview_question_text(container)

                    if not question_text:
                        print(f"第 {i} 题无法提取题目内容，跳过")
                        continue

                    print(f"第 {i} 题类型: {question_type}")
                    print(f"第 {i} 题内容: {question_text[:100]}...")

                    # 根据题目类型填充答案
                    success = False
                    if question_type == "单选题":
                        success = self.fill_preview_single_choice(container, question_id, question_text)
                    elif question_type == "多选题":
                        success = self.fill_preview_multiple_choice(container, question_id, question_text)
                    elif question_type == "判断题":
                        success = self.fill_preview_true_false(container, question_id, question_text)
                    elif question_type == "填空题":
                        success = self.fill_preview_fill_blank(container, question_id, question_text)
                    elif question_type == "简答题":
                        success = self.fill_preview_essay(container, question_id, question_text)
                    else:
                        print(f"第 {i} 题类型未知: {question_type}")
                        continue

                    if success:
                        filled_count += 1
                        print(f"第 {i} 题填充成功")
                        # 答题间隔
                        time.sleep(random.uniform(1, 2))
                    else:
                        print(f"第 {i} 题填充失败")

                except Exception as e:
                    print(f"处理第 {i} 题时出错: {e}")
                    continue

            print(f"整卷预览检查完成，共填充 {filled_count} 道题目")
            return filled_count

        except Exception as e:
            print(f"检查遗漏答案时出错: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def is_preview_question_answered(self, container, question_id):
        """
        检查预览页面中的题目是否已作答
        """
        try:
            # 查找答案输入字段
            answer_inputs = container.find_elements(By.CSS_SELECTOR, f'input[id*="answer{question_id}"], input[name*="answer{question_id}"]')

            for input_elem in answer_inputs:
                value = input_elem.get_attribute('value')
                if value and value.strip():
                    return True

            # 检查选中的选项
            checked_options = container.find_elements(By.CSS_SELECTOR, 'div[aria-checked="true"], input[checked]')
            if checked_options:
                return True

            # 检查文本域
            textareas = container.find_elements(By.CSS_SELECTOR, 'textarea')
            for textarea in textareas:
                value = textarea.get_attribute('value')
                if value and value.strip():
                    return True

            return False

        except Exception as e:
            print(f"检查题目是否已作答时出错: {e}")
            return False

    def detect_preview_question_type(self, container):
        """
        检测预览页面中题目的类型
        """
        try:
            # 查找题目类型标识
            type_elements = container.find_elements(By.CSS_SELECTOR, '.colorShallow, .mark_name .colorShallow')
            for elem in type_elements:
                text = elem.text
                if "单选题" in text:
                    return "单选题"
                elif "多选题" in text:
                    return "多选题"
                elif "判断题" in text:
                    return "判断题"
                elif "填空题" in text:
                    return "填空题"
                elif "简答题" in text or "论述题" in text:
                    return "简答题"

            # 根据输入元素类型判断
            if container.find_elements(By.CSS_SELECTOR, 'input[type="radio"]'):
                return "单选题"
            elif container.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]'):
                return "多选题"
            elif container.find_elements(By.CSS_SELECTOR, 'textarea'):
                return "简答题"

            return "未知"

        except Exception as e:
            print(f"检测题目类型时出错: {e}")
            return "未知"

    def extract_preview_question_text(self, container):
        """
        提取预览页面中的题目内容
        """
        try:
            # 查找题目内容
            question_selectors = [
                '.mark_name',
                '.stem_answer',
                '.questionLi > h3',
                '.question-content'
            ]

            for selector in question_selectors:
                elements = container.find_elements(By.CSS_SELECTOR, selector)
                for elem in elements:
                    text = elem.text.strip()
                    if text and len(text) > 10:  # 确保是有效的题目内容
                        # 清理题目编号和类型标识
                        import re
                        text = re.sub(r'^\d+\.\s*', '', text)  # 移除题目编号
                        text = re.sub(r'\(.*?题.*?\)', '', text)  # 移除题目类型标识
                        return text.strip()

            return ""

        except Exception as e:
            print(f"提取题目内容时出错: {e}")
            return ""

    def fill_preview_single_choice(self, container, question_id, question_text):
        """
        在预览页面填充单选题答案
        """
        try:
            if self.ai_answer_enabled:
                # 使用AI获取答案
                ai_answer = self.get_ai_answer(question_text, "单选题")
                if ai_answer:
                    print(f"AI返回答案: {ai_answer}")

                    # 查找选项并点击
                    options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
                    for option in options:
                        option_text = option.text.strip()
                        if option_text.startswith(ai_answer.upper()):
                            option.click()
                            print(f"已选择选项: {option_text}")
                            return True

            # 如果AI答案无效，选择第一个选项
            options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
            if options:
                options[0].click()
                print("已选择第一个选项（默认）")
                return True

            return False

        except Exception as e:
            print(f"填充预览单选题时出错: {e}")
            return False

    def fill_preview_multiple_choice(self, container, question_id, question_text):
        """
        在预览页面填充多选题答案
        """
        try:
            if self.ai_answer_enabled:
                # 使用AI获取答案
                ai_answer = self.get_ai_answer(question_text, "多选题")
                if ai_answer:
                    print(f"AI返回答案: {ai_answer}")

                    # 解析AI答案（如：ABCD）
                    selected_options = list(ai_answer.upper())

                    # 查找选项并点击
                    options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
                    for i, option in enumerate(options):
                        option_letter = chr(65 + i)  # A, B, C, D...
                        if option_letter in selected_options:
                            option.click()
                            print(f"已选择选项: {option_letter}")

                    return True

            # 如果AI答案无效，选择前两个选项
            options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
            if len(options) >= 2:
                options[0].click()
                options[1].click()
                print("已选择前两个选项（默认）")
                return True

            return False

        except Exception as e:
            print(f"填充预览多选题时出错: {e}")
            return False

    def fill_preview_true_false(self, container, question_id, question_text):
        """
        在预览页面填充判断题答案
        """
        try:
            if self.ai_answer_enabled:
                # 使用AI获取答案
                ai_answer = self.get_ai_answer(question_text, "判断题")
                if ai_answer:
                    print(f"AI返回答案: {ai_answer}")

                    # 查找选项并点击
                    options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
                    for option in options:
                        option_text = option.text.strip()
                        if ("正确" in option_text or "对" in option_text) and ("正确" in ai_answer or "对" in ai_answer):
                            option.click()
                            print("已选择：正确")
                            return True
                        elif ("错误" in option_text or "错" in option_text) and ("错误" in ai_answer or "错" in ai_answer):
                            option.click()
                            print("已选择：错误")
                            return True

            # 如果AI答案无效，默认选择正确
            options = container.find_elements(By.CSS_SELECTOR, '.answerBg')
            for option in options:
                option_text = option.text.strip()
                if "正确" in option_text or "对" in option_text:
                    option.click()
                    print("已选择：正确（默认）")
                    return True

            return False

        except Exception as e:
            print(f"填充预览判断题时出错: {e}")
            return False

    def fill_preview_fill_blank(self, container, question_id, question_text):
        """
        在预览页面填充填空题答案
        """
        try:
            if self.ai_answer_enabled:
                # 使用AI获取答案
                ai_answer = self.get_ai_answer(question_text, "填空题")
                if ai_answer:
                    print(f"AI返回答案: {ai_answer}")

                    # 查找输入框并填入答案
                    inputs = container.find_elements(By.CSS_SELECTOR, 'input[type="text"], textarea')
                    if inputs:
                        # 如果有多个空，按###分割答案
                        answers = ai_answer.split('###') if '###' in ai_answer else [ai_answer]

                        for i, input_elem in enumerate(inputs):
                            if i < len(answers):
                                input_elem.clear()
                                input_elem.send_keys(answers[i].strip())
                                print(f"已填入第{i+1}个空: {answers[i].strip()}")

                        return True

            return False

        except Exception as e:
            print(f"填充预览填空题时出错: {e}")
            return False

    def fill_preview_essay(self, container, question_id, question_text):
        """
        在预览页面填充简答题答案
        """
        try:
            if self.ai_answer_enabled:
                # 使用AI获取答案
                ai_answer = self.get_ai_answer(question_text, "简答题")
                if ai_answer:
                    print(f"AI返回答案: {ai_answer}")

                    # 查找文本域并填入答案
                    textareas = container.find_elements(By.CSS_SELECTOR, 'textarea')
                    if textareas:
                        textareas[0].clear()
                        textareas[0].send_keys(ai_answer)
                        print("已填入简答题答案")
                        return True

            return False

        except Exception as e:
            print(f"填充预览简答题时出错: {e}")
            return False

    def check_exam_completion(self):
        """
        检查考试是否完成
        """
        try:
            print("检查考试完成状态...")

            # 查找提交按钮
            submit_selectors = [
                '.submitBtn',
                '#submitBtn',
                'input[type="submit"]',
                'button[onclick*="submit"]',
                '.btn-submit',
                'a[onclick*="submit"]',
                '.jb_btn[onclick*="submit"]'
            ]

            submit_button = None
            for selector in submit_selectors:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for button in buttons:
                    if "提交" in button.text or "submit" in button.text.lower():
                        submit_button = button
                        print(f"找到提交按钮: {selector}")
                        break
                if submit_button:
                    break

            if submit_button:
                print("发现提交按钮，考试可能已完成")
                if self.auto_submit:
                    print("自动提交考试...")
                    submit_button.click()
                    print("考试已提交")
                else:
                    print("请手动提交考试")
                return True
            else:
                print("未找到提交按钮，考试可能还未完成")
                return True

        except Exception as e:
            print(f"检查考试完成状态时出错: {e}")
            return True

    def run(self):
        """
        运行考试自动填充
        """
        try:
            print("=== 超星学习通考试自动填充答案 ===")

            # 登录和设置浏览器
            if not self.login_and_setup():
                return False

            # 进入考试
            if not self.enter_exam():
                return False

            # 处理题目
            if not self.auto_answer_exam():
                return False

            print("\n=== 考试自动填充完成 ===")

            if not self.auto_submit and not self.save_only:
                print("请手动检查答案并提交考试")
                if not self.headless:
                    input("按回车键关闭浏览器...")

            return True

        except Exception as e:
            print(f"运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if self.driver:
                self.driver.quit()


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="超星学习通考试自动填充答案脚本")
    parser.add_argument("-u", "--username", required=True, help="超星学习通用户名")
    parser.add_argument("-p", "--password", required=True, help="超星学习通密码")
    parser.add_argument("--params", required=True, help="考试参数，格式：examId|courseId|classId|cpi")
    parser.add_argument("--show-browser", action="store_true", help="显示浏览器窗口（用于调试）")
    parser.add_argument("--headless", action="store_true", help="无头模式运行（不显示浏览器窗口）")
    parser.add_argument("--ai-answer", action="store_true", help="启用AI答题功能")
    parser.add_argument("--save-only", action="store_true", help="只保存答案，不提交考试")
    parser.add_argument("--auto-submit", action="store_true", help="自动提交考试")
    parser.add_argument("--tiku-url", help="自定义题库API地址")
    parser.add_argument("--debug-captcha", action="store_true", help="启用验证码调试模式（显示浏览器，查看识别结果）")

    args = parser.parse_args()

    if not SELENIUM_AVAILABLE:
        print("错误: Selenium未安装，无法使用浏览器自动化功能")
        print("请运行: pip install selenium")
        return

    if not HOMEWORK_MANAGER_AVAILABLE:
        print("错误: chaoxing_homework_manager模块未找到，无法使用登录功能")
        print("请确保chaoxing_homework_manager.py文件在同一目录下")
        return

    try:
        # 确定是否使用无头模式
        # 如果指定了--headless，则使用无头模式
        # 如果指定了--show-browser，则显示浏览器
        # 默认使用无头模式
        if args.headless:
            headless_mode = True
        elif args.show_browser:
            headless_mode = False
        else:
            headless_mode = True  # 默认无头模式

        # 创建考试自动填充器
        filler = ExamAutoFiller(
            username=args.username,
            password=args.password,
            params=args.params,
            tiku_url=args.tiku_url,
            headless=headless_mode,
            auto_submit=args.auto_submit,
            ai_answer=args.ai_answer,
            save_only=args.save_only,
            debug_captcha=args.debug_captcha
        )

        # 运行自动填充
        success = filler.run()

        if success:
            print("考试自动填充成功完成")
        else:
            print("考试自动填充失败")

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
