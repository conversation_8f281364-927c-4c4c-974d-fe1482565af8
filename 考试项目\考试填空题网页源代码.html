
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<script>
    _HOST_ = "//mooc1.chaoxing.com";
    _CP_ = "/exam-ans";
    _HOST_CP1_ = "//mooc1.chaoxing.com/exam-ans";
    // _HOST_CP2_ = _HOST_ + _CP_;
    _HOST_CP2_ = _CP_;
</script><head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>考试</title>
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/pop.css?v=2021-0311-1412"/>
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/commonStuExam.css?v=2025-0424-1038"/>
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/q_marking_icon.css"/>
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/style.css?v=2025-0424-1038" >
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/exam-viewStudent.css?v=2025-0526-1542"/>
    <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/newBuiltStudent.css?v=2025-0519-1746"/>
	        <link rel="stylesheet" type="text/css" href="//mooc1.chaoxing.com/exam-ans/mooc2/css/notAllowCopy.css"/>
    			<link rel="stylesheet" type="text/css" href="/exam-ans/spacx/exam/mooc2/css/do/exam-do.css?v=**************"/>
	<link href="/exam-ans/spacx/new/css/msg-note-tpl.cssx?enc=ec187eba3b835c9caa96a1672a828912" rel="stylesheet" type="text/css"/>
	    <link href="//mooc1.chaoxing.com/exam-ans/css/questionBank/questionBankUsual.css?v=**************" type="text/css" rel="stylesheet"/></head>
<script>
	window["uid"] = '*********';
	window["currentTime"] = '*************';
	window["uploadEnc"] = '1f61bc3c4030f9127d05e35805e0b8d6';
</script>
<script type="text/javascript">
    I18N = {
        "confirmSub": "确认交卷？",
        "subFailed": "提交失败",
        "subSucc": "提交成功",
        "worksubmitTips1": "您还有未做完的",
        "worksubmitTips2": "，确认提交吗？",
        "notLeaveTip1": "系统检测到你于",
        "notLeaveTip2": "离开考试，请不要中途离开。",
        'recording': '录制中',
        'start2': '开始',
        'faceInDiv': '请保持光线良好，面对镜头眨眨眼，保证人脸在识别框内',
        'feedbackEmpty': '问题反馈不能为空',
        'nocamera': '未检测到您的摄像头，请确认开启摄像头权限',
        'dectShareScreen': '检测到您未能开启截屏权限，请重新检测，点击"截屏授权"，在浏览器弹窗中共享"整个屏幕',
        'feedbackSuccess': '反馈成功',
        'liveAccount' : '直播账号',
        'passwordI18N' : '密码',
        'archivePhoto' : '档案照片',
        'noArchivePhoto' : '暂无档案照片',
		'forceSubmitTip':'考试已被提交',
        'forceSubmitStyle1Tip':'你已被老师收卷',
        'allowAnswerBackTips':'本场考试教师已设置不允许查看上一题，当前题目未作答，确认继续作答下一题？',
		"faceRcognitionFailTip": "人脸照片采集失败，请重试",
        "serviceExceptionTip": "服务异常，请重新开始",
        "faceRcognitionCompareSucceTip": "对比相似度成功，正在进入考试...",
        "faceRcognitionCompareFailTip": "对比相似度失败",
        "faceRcognitionLiveStatusFailTip": "人脸活体识别未通过，匹配异常",
        "electronFaceRcognitionCompareSucceTip": "对比相似度成功，请进行下一步",
        "reIdentify": "重新识别",
        "saveTheAnswer": '请保存答案',
        "mac_microphonePermissionTip": '考试客户端系统权限未完全配置，麦克风功能可能受限，无法正常进入考试。请打开【系统设置】，进入【隐私与安全性】，允许“考试客户端”应用程序访问你的麦克风。若权限打开后未检测正常，可能需要退出重启客户端。',
        "mac_screenPermissionTip": '考试客户端系统权限未完全配置，屏幕录制功能可能受限，无法正常进入考试。请打开【系统设置】，进入【隐私与安全性】，允许“考试客户端”应用程序访问你的屏幕录制。若权限打开后未检测正常，可能需要退出重启客户端。',
        "mac_cameraPermissionTip": '考试客户端系统权限未完全配置，摄像头功能可能受限，无法正常进入考试。请打开【系统设置】，进入【隐私与安全性】，允许“考试客户端”应用程序访问你的摄像头。若权限打开后未检测正常，可能需要退出重启客户端。',
        "win_microphonePermissionTip": '考试客户端系统权限未完全配置，麦克风功能可能受限，无法正常进入考试。请点击【开始】进入【设置】，进入【隐私和安全性】，允许“考试客户端”应用访问你的麦克风。',
        "win_screenPermissionTip": '考试客户端系统权限未完全配置，屏幕录制功能可能受限，无法正常进入考试。请点击【开始】进入【设置】，进入【隐私和安全性】，允许“考试客户端”应用访问你的屏幕录制。',
        "win_cameraPermissionTip": '考试客户端系统权限未完全配置，摄像头功能可能受限，无法正常进入考试。请点击【开始】进入【设置】，进入【隐私和安全性】，允许“考试客户端”应用访问你的摄像头。',
        "enterCode": '请输入程序代码！',
        "selectProgramLang": '请选择程序语言',
		 "exitCountExamLockTipPrefix":"系统检测到你离开考试",
    	 "exitCountExamLockTipSuffix":"次，已锁定考试",
    	 "exitDurationExamLockTipPrefix":"系统检测到你的切屏时长超过",
    	 "exitDurationExamLockTipSuffix":"秒，已锁定考试",
		 "screenshotNumberLockTipPrefix":"系统检测到你截屏次数达到",
    	 "screenshotOccupancyNumberLockTipPrefix":"系统检测到你屏幕权限被占用次数达到",
    	 "screenshotLockTipSuffix":"次，已锁定考试"
    };
</script><body onselectstart="return false" class="whiteBg">
<style>
    .questionLi img{max-width:100%;}
</style>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery.min.js"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/common/jquery-migrate.min.js"></script><script type="text/javascript" src="/mooc2/js/codemirror/js/codemirror.js"></script>

<script type="text/javascript" src="/mooc2/js/codemirror/js/clike.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/go.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/css.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/python.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/sql.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/xml.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/highlight.min.js"></script>

<script type="text/javascript" src="/mooc2/js/codemirror/js/anyword-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/beautify.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/brace-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/clike.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/closebrackets.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/closetag.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/comment-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/foldcode.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/foldgutter.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/go.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/indent-fold.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/javascript-lint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/jshint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/lint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/matchbrackets.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/php.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/powershell.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/python.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/ruby.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/show-hint.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/swift.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/vb.min.js"></script>
<script type="text/javascript" src="/mooc2/js/codemirror/js/xml.min.js"></script>

<link rel="stylesheet" type="text/css" href="/mooc2/js/codemirror/lib/codemirror.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/foldgutter.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/lint.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/show-hint.min.css?v=2024-1101-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/common.css?v=2025-0314-1900"/>
<link rel="stylesheet" type="text/css" href="/mooc2/css/codemirror/selfTesting.css?v=2025-0704-1801"/>

<script src="//mooc1.chaoxing.com/exam-ans/js/jquery.jqote2.min.js" ></script>

<script type="text/javascript" src="/mooc2/js/codemirror/codeEditor.js?v=2025-0320-1130"></script>
<script>
    window.noNeedCodemirrorInUeditor = true;
</script>
<script type="text/x-jqote-template" id="codeEditorTpl">
    <%
    const currLanguageName = this.currLanguageName || '请选择程序语言';
    const currLanguageIndex = this.currLanguageIndex || -1;
    const languages = this.languages;
    const businessId = this.businessId;
    %>

    <!--弹窗-->
    <div class="maskBox reset-mask" data-business-id="<%= businessId %>" style="display:none">
        <div class="promptPop">
            <div class="popHeadNew">
                <a href="#" class="popClose fr" data-business-id="<%= businessId %>"><img src="//mooc1.chaoxing.com/exam-ans/images/popClose.png"></a>
                <p>提示</p>
            </div>
            <p class="popWord2">代码框将重置为初始状态，正在编辑的代码将清空，确认重置代码？</p>
            <div class="popBottomNew">
                <a href="#" class="popbtn_bg btnReset fr" data-business-id="<%= businessId %>">重置代码</a>
                <a href="#" class="popbtn_border btnCancle fr" data-business-id="<%= businessId %>">取消</a>
            </div>
        </div>
    </div>

    <div class="maskBox setting-mask" data-business-id="<%= businessId %>" style="display:none">
        <div class="promptPop">
            <div class="popHeadNew">
                <a href="#" class="popClose fr" data-business-id="<%= businessId %>"><img src="//mooc1.chaoxing.com/exam-ans/images/popClose.png"></a>
                <p>代码编辑器设置</p>
            </div>
            <div class="popWord2">
                <div class="flex-start">
                    <span>字体设置</span>
                    <strong class="selectBox fontList" data-business-id="<%= businessId %>">
                        <p value="0"><span>14px</span><i class="icon-arrow-down2"></i></p>
                        <ul class="options optionsCon" style="display:none">
                            <li><a href="javascript:;">12px</a></li>
                            <li><a href="javascript:;">14px</a></li>
                            <li><a href="javascript:;">16px</a></li>
                            <li><a href="javascript:;">18px</a></li>
                            <li><a href="javascript:;">20px</a></li>
                            <li><a href="javascript:;">24px</a></li>
                            <li><a href="javascript:;">36px</a></li>
                            <li><a href="javascript:;">48px</a></li>
                        </ul>
                    </strong>
                </div>
            </div>
            <div class="popBottomNew">
                <a href="#" class="popbtn_bg btnSetting fr" data-business-id="<%= businessId %>">确认</a>
                <a href="#" class="popbtn_border btnCancle fr" data-business-id="<%= businessId %>">取消</a>
            </div>
        </div>
    </div>

    <div class="mrconBx" data-business-id="<%= businessId %>">
        <div class="mrconTop">
            <div>
                <i class="icon-com fr setting" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>设置</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr copy" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>复制</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr refresh" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>重置</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>
                <i class="icon-com fr format" data-business-id="<%= businessId %>">
                    <div class="Vector-pop">
                        <span>格式化</span>
                        <div class="triangle-container">
                            <div class="triangle"></div>
                        </div>
                    </div>
                </i>

                <% if(currLanguageIndex!== -1) { %>
                <!--mooc2创建的程序题-->
                <strong class="selectBoxLang fl langList" codenum="<%= currLanguageIndex %>" codename="<%= currLanguageName %>" data-business-id="<%= businessId %>">
                    <p value="0"><span><%= currLanguageName %></span><i class="icon-arrow-down2"></i></p>
                    <ul class="options " style="display:none">
                        <% for(const pair of languages) { %>
                        <li><a href="javascript:void(0)" codename="<%= pair.codeName %>" code="<%= pair.code %>" codenum="<%= pair.codenum %>"><%= pair.codeName %></a></li>
                        <% } %>
                    </ul>
                </strong>
                <% } else { %>
                <!--mooc1创建的程序题 || 新建的程序题-->
                <strong class="selectBoxLang fl langList" data-business-id="<%= businessId %>">
                    <p value="0"><span>请选择程序语言</span><i class="icon-arrow-down2"></i></p>
                    <ul class="options " style="display:none">
                        <% for(const pair of languages) { %>
                        <li><a href="javascript:void(0)" codename="<%= pair.codeName %>" code="<%= pair.code %>" codenum="<%= pair.codenum %>"><%= pair.codeName %></a></li>
                        <% } %>
                    </ul>
                </strong>
                <% } %>

                <p class="clear"></p>
            </div>
        </div>
    </div>


</script>
<input type="hidden" id="submitLimitTime" name="submitLimitTime" value="20"/>
<input type="hidden" id="limitTime" name="limitTime" value="60"/>
<input type="hidden" id="wordNum" name="wordNum" value="0"/>
<input type="hidden" id="qbanksystem" name="qbanksystem" value="0"/>
<input type="hidden" id="qbankbackurl" name="qbankbackurl" value=""/>
<input type="hidden" id="entryExamTime" value="">
<input type="hidden" id="rentryExamTime" value="">
<input type="hidden" id="switchScreenControl" name="switchScreenControl" value="1"/>
<input type="hidden" id="snapshotMonitor" name="snapshotMonitor" value="0"/>
<input type="hidden" id="faceDetectionResult" name="faceDetectionResult" value=""/>
<input type="hidden" id="pcclientSwitchout" name="pcclientSwitchout" value="0"/>
<input type="hidden" id="saveUrl" name="saveUrl" value="/exam-ans/exam/test/reVersionSubmitTestNew"/>
<input type="hidden" id="monitorEnc" name="monitorEnc" value="f188b59ad32dc0e4b369980d428d0a13"/>
<input type="hidden" id="uploadTimeStamp" value="*************" />
<input type="hidden" id="uploadEnc" value="1f61bc3c4030f9127d05e35805e0b8d6" />
<input type="hidden" id="uploadtype" value="exam" />
<input type="hidden" id="openc" value="32b95692130f772c1f237eb6146cf0bc" />
<input type="hidden" id="allowPaste" value="0"/>
<input type="hidden" id="allowDownloadAttachment" value="1"/>
<input type="hidden" id="receiveTime" value="1753705349000"/>
<input type="hidden" id="limitTimeType" value="0"/>
<input type="hidden" id="singleQuesLimitTime" value=""/>
<input type="hidden" id="forbidAnsweredAgain" value="0"/>
<input type="hidden" id="forbidGroupAnswerBack" value="0"/>
<input type="hidden" id="groupLimitTimeType" value="0"/>
<input type="hidden" id="groupMinLimitTime" value=""/>
<input type="hidden" id="groupMaxLimitTime" value=""/>
<input type="hidden" id="groupReceiveTime" value="0"/>
<input type="hidden" id="thisQuestionGroupId" value="0"/>
<input type="hidden" id="prevQuestionGroupId" value=""/>
<input type="hidden" id="nextQuestionGroupId" value=""/>
<input type="hidden" id="nextGroupPaperId" value=""/>
<input type="hidden" id="nextGroupIndexDiff" value=""/>
<input type="hidden" id="checkGroupPop" value="0"/>
<input type="hidden" id="groupStart" value="0"/>

<input type="hidden" id="workExamUploadUrl" name="workExamUploadUrl" value=""/>
<input type="hidden" id="workExamUploadCrcUrl" name="workExamUploadCrcUrl" value=""/>

<input type="hidden" id="snapshotIntervalMin" value=""/>
<input type="hidden" id="snapshotIntervalMax" value=""/>

<input type="hidden" id="ExamWaterMark" value="张克平202401051482"/>
<input type="hidden" id="isChaoxingExamPc" value=""/>


<input type="hidden" id="webSnapshotMonitor" value="0"/>
<input type="hidden" id="faceimgs" value=""/>
<input type="hidden" id="screenAbnormalNumberLimit" value="0"/>
<input type="hidden" id="switchScreenNumberLimit" name="switchScreenNumberLimit" value="0"/>
<input type="hidden" id="switchScreenDurationLimit" name="switchScreenDurationLimit" value="0"/>
<input type="hidden" id="switchScreenThresholdTime" value="0"/>
<input type="hidden" id="monitorStatus" name="monitorStatus" value="0"/><input type="hidden" id="monitorOp" name="monitorOp" value="-1"/>
<input type="hidden" id="exitCount"  value="0"/>
<input type="hidden" id="exitDuration" value="0"/>

<input type="hidden" id="screenAndCaptureCall" value="-1"/>
<input type="hidden" id="xiaMenUniversity" value=""/>

<input type="hidden" id="start" value="40" />
<input type="hidden" id="isAccessibleCustomFid" name="isAccessibleCustomFid" value="0">

<input type="hidden" id="liveMonitor" name="liveMonitor" value="0"/>
<input type="hidden" id="electronSnapshotMonitor" value="0"/>

<input type="hidden" id="allowAnswerBack" value="1"/>
<input type="hidden" id="showNextPop" value="false"/>
<input type="hidden" id="forbidInputText" value="0"/>
<input type="hidden" id="answerTimes" name="answerTimes" value="18"/>
<input type="hidden" id="examLocalStorageSwitch" name="examLocalStorageSwitch" value="1"/>
<input type="hidden" id="examTimedSaveThreshold" name="examTimedSaveThreshold" value="1"/>
<input type="hidden" id="examAttachAutoSave" name="examAttachAutoSave" value="1"/>
<input type="hidden" id="checkExamBrowerBack" name="checkExamBrowerBack" value="1"/>
<input type="hidden" id="selftestMode" name="selftestMode" value="0"/>
<input type="hidden" id="pcExamClientSign"  value="true"/>
<input type="hidden" id="monitorLock" value="0"/>
<input type="hidden" id="answerLocked" value="0"/>
<input type="hidden" id="isExamPage" value="1"/>
<input type="hidden" id="forbidCloudFile" value="0"/>
<input type="hidden" id="signConfig" value=""/>
<input type="hidden" id="signversion" value="0"/>
<input type="hidden" id="screenshotNumberLimit" value="0"/>
<input type="hidden" id="screenshotNumber" value="0"/>
<input type="hidden" id="screenshotOccupancyNumberLimit" value="0"/>
<input type="hidden" id="screenshotOccupancyNumber" value="0"/>
<input type="hidden" id="screenRecordDuration" value="0"/>
<input type="hidden" id="screenRecordInterval" value="0"/>
<input type="hidden" id="receiveScreenRecordLogUrl" value=""/>
<input type="hidden" id="exitexamForcesubmit" value="0"/>

<input type="hidden" id="studentInfoUrl" value=""/>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/exam-ans/js/ServerHost.js?v=2020-1225-1627"></script>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/editor/ueditor.config.js?v=**************"></script>
<script type="text/javascript" charset="utf-8" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/editor/ueditor.all.min.js?v=**************"></script>
<script>
    function getcookie(objname){
    	var arrstr = document.cookie.split("; ");
    	for(var i = 0;i < arrstr.length;i ++){
    		var temp = arrstr[i].split("=");
    		if(temp[0] == objname){ 
    			return unescape(temp[1]);
    		}
    	}
    }
	window["uid"] = '*********';
	window["currentTime"] = '*************';
	window["uploadEnc"] = '1f61bc3c4030f9127d05e35805e0b8d6';
	window.UEDITOR_CONFIG.forbidDownload = 'false';
	window.UEDITOR_CONFIG.scaleEnabled = false;
	window.UEDITOR_CONFIG.imageUrl = ServerHost.uploadDomain + "/ueditorupload/upload?t=*************&enc2=1f61bc3c4030f9127d05e35805e0b8d6&uid=*********";
	window.UEDITOR_CONFIG.fileUrl = ServerHost.uploadDomain + "/ueditorupload/attachment";
	window.UEDITOR_CONFIG.lang = 'zh-cn';
	window.fycourseDomain = "mobile3.chaoxing.com";

	var isMirror = "false";
	if (isMirror == "true") {
		window.UEDITOR_CONFIG.toolbars = [['removeformat', 'formatmatch', 'paragraph', 'fontfamily', 'fontsize', 'bold', 'italic', 'underline', 'forecolor', 'justifyleft', 'justifycenter', 'justifyright', 'spechars', 'inserttable', 'mathml', 'edrawmath', 'insertimage', 'attachment_new', 'recording', 'audio', 'drawingboard', 'insertcodedefined']];
	}

	    	var toolbars = window.UEDITOR_CONFIG.toolbars[0];
		if($.inArray('mathml', toolbars) >= 0){
            toolbars.splice($.inArray('mathml', toolbars) , 1,'latex');
            window.UEDITOR_CONFIG.toolbars[0] = toolbars;
            window.UEDITOR_CONFIG.latexMode = 1;
		}
	
		    window.UEDITOR_CONFIG._mathpix2latex = '*********-*********-*********-s_2eee2981c2f78d931a937294b55f9742';
	
	</script><script>window.UEDITOR_CONFIG.autoFloatEnabled = false; </script>
<script type="text/javascript" charset="utf-8">
    if('0' == 0){
        window.UEDITOR_CONFIG.disableDraggable = true;
        window.UEDITOR_CONFIG.disablePasteImage = true;
    }
	
	    	var toolbars = window.UEDITOR_CONFIG.toolbars[0];
        $(["insertimage","attachment_new","audio"]).each(function(index,obj){
           toolbars.splice($.inArray(obj, toolbars), 1);
        });
    	window.UEDITOR_CONFIG.toolbars[0] = toolbars;
    
         	var toolbars = window.UEDITOR_CONFIG.toolbars[0];
        toolbars.splice($.inArray("tpupload", toolbars), 1);
    	window.UEDITOR_CONFIG.toolbars[0] = toolbars;
    
         	var toolbars = window.UEDITOR_CONFIG.toolbars[0];
        toolbars.splice($.inArray("recording", toolbars), 1);
    	window.UEDITOR_CONFIG.toolbars[0] = toolbars;
    
		window.UEDITOR_CONFIG.imagePopup = false;
</script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poptoast.js?v=2023-0201-1903"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/poplayout.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/exam/phone/date.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/jquery.jqote2.min.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/richvideoedit/json2.js"></script>
<script src="/ananas/space/exam/js/enc_js_exam.js?v=1753706303978"></script>
<script src="//mooc1.chaoxing.com/exam-ans/safefilter/ajax-unlock.js?v=2024-0110-1404"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/jquery.nicescroll.min.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/selectBox.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/views/exam/phone/s/v3/js/textareaHeightAuto-test.js"></script>


<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-do-exam.js?v=2025-0605-1906"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/stu-exam-share.js?v=2025-0605-1000"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/pc-exam-sign-min.js?v=2025-0606-2200"></script>

<script type="text/javascript">window.SUPPORT_AUDIO_SETTING = true; window.SUPPORT_AUDIO_CONTROL = true; window.Forbid_Attachment_Title = '0';</script>
<div class="subNav top-subNav"  tabindex="0" role="option" aria-label="考试 页面">
	    		<div class="sub-button fr" >
					<a href="javascript:;" class="completeBtn fl" onclick="topreview();"  >整卷预览</a>
				</div>
	 考试</div><div class="het40"></div>
<div class="FullfanyaMarking TiMu" id="fanyaMarking">
	
			
		
	<div class="marking_left_280" id="tabMarkingLeft">
		<div class="padlr24">
			<h2 class="h2_subject" tabindex="0" role="option" >大学生安全教育测试</h2>
			<div class="mark_timeDiv"  tabindex="0" role="option" ><i><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/time_orange.png" class="no_view" tabindex="-1" aria-hidden="true" ></i><span id="timer">&nbsp;</span></div>
						<dl class="mark_dl" tabindex="0" role="option" >
				 				<dd>
					<p>姓名: 张克平</p>
					<p>学号: 202401051482</p>
									</dd>
			</dl>
			<div class="mark_info" tabindex="0" role="option" >
				<p>题量:  85 </p>				<p>满分: 100.0</p>				<p>考试时间: 2025-07-02 19:09  至  2025-07-28 23:59</p>				    																		</div>
            <!--------直播账号提示--------->
					</div>
		
		<div style="display:none;">
			<video id="screenCapture_video"></video>
			<canvas id="screenCapture_canvas"></canvas>
		    <img id="screenCapture_photo" class="no_view"/>
	   </div>

				
	</div>
		
		
		
		<div class="marking_content">

			
		<div class="mark_table">
									
						<div class="whiteDiv questionLi singleQuesId ans-cc mainhet" data="884250017">
							   			   <div  class="noSplitBx"  >

								<div class="splitS-left">
														   																																															<h2 class="type_tit" tabindex="0" role="option" >三、 填空题 （共 20 题，20.0 分） </h2>
																														   																		<h3 class="mark_name colorDeep" tabindex="-1" aria-hidden="true" >41.
												<span class="colorShallow" tabindex="0" role="option" aria-label='41. (填空题, 1.0 分)' >
							(填空题, 1.0 分)
						</span>
						  
						    						<div style="overflow:hidden;">
    							    								<p>生产经营单位应当具备本法和有关法律、行政法规和国家标准或者行业标准规定的____;不具备的，不得从事生产经营活动。</p>
								    					    </div>
									  </h3>
								</div>

								<div class="splitS-right">
				<form id="submitTest" action="/exam-ans/exam/test/reVersionSubmitTestNew?keyboardDisplayRequiresUserAction=1&classId=*********&courseId=*********&testPaperId=7689933&testUserRelationId=*********" method="post" >
					<input type="hidden" id="courseId" name="courseId" value="*********"/>
					<input type="hidden" id="paperId" name="paperId" value="453977842"/>
					<input type="hidden" id="testPaperId" name="testPaperId" value="7689933"/>
					<input type="hidden" id="examCreateUserId" name="examCreateUserId" value="205814476"/>
					<input type="hidden" id="feedbackEnc" name="feedbackEnc" value=""/>
					<input type="hidden" id="testUserRelationId" name="testUserRelationId" value="*********"/>
					<input type="hidden" id="tId" name="tId" value="7689933"/>
					<input type="hidden" id="subCount" name="subCount" value=""/>
					<input type="hidden" id="remainTime" name="remainTime" value="2645"/>
					<input type="hidden" id="encRemainTime" name="encRemainTime" value="2646"/>
					<input type="hidden" id="encLastUpdateTime" name="encLastUpdateTime" value="1753706303891"/>
					<input type="hidden" id="tempSave" name="tempSave" value="false"/>
					<input type="hidden" id="timeOver" name="timeOver" value="false"/>
					<input type="hidden" id="type" name="type" value="0"/>
					<input type="hidden" id="classId" name="classId" value="*********"/>
					<input type="hidden" id="enc" name="enc" value="8266cdf55681669ebd06be57ab667d6e"/>
					<input type="hidden" id="examsystem" name="examsystem" value="0" />
					<input type="hidden" name="start" value="40" />
					<input type="hidden" id="userId" name="userId" value="*********"/>
					<input type="hidden" id="randomOptions" name="randomOptions" value="true"/>
					<input type="hidden" id="cpi" name="cpi" value="*********">
					<input type="hidden" id="openc" name="openc" value="32b95692130f772c1f237eb6146cf0bc">
					<input type="hidden" id="enterPageTime" name="enterPageTime" value="*************"/>
					<input type="hidden" name="questionId" id="questionId" value="884250017"/>
					<input type="hidden" name="questionScore" id="questionScore" value="1.0"/>
					<input type="hidden" name="type884250017" value="2"/>
					<input type="hidden" name="score884250017" value="1.0"/>
					<input type="hidden" id="exitdtime" name="exitdtime" value="0"/>
	                <input type="hidden" name="monitorforcesubmit" id="monitorforcesubmit" value="0"/>
					<input type="hidden" id="answeredView" name="answeredView" value="0"/>
					<input type="hidden" id="isAnswered" value="false"/>
					<input type="hidden" id="paperGroupId" name="paperGroupId" value="0"/>
																				<input type="hidden" id="typeName884250017" name="typeName884250017" value="填空题"/>
					 						<div class="stem_answer">
                            
                <div class="Answer">
                    <span class="tiankong fl">第1空</span>
                    <div class="divText fl">
                        <div class="textDIV subEditor">
                                                                                            <textarea name="answerEditor8842500171" id="answerEditor8842500171"></textarea>
                                <script>
                                    window.UEDITOR_CONFIG.initialFrameWidth = 'auto';
                                    window.UEDITOR_CONFIG.initialFrameHeight = 150;
                                    var editor884250017 = UE.getEditor("answerEditor8842500171",{'initialFrameHeight':56,'toolbars':[],'pasteplain':true,'disablePasteImage':true,'disableDraggable':true});

                                    //富媒体框禁止粘贴
                                    var blankobj = "1";
                                    var allowPaste = "0";
                                    if(parseInt(allowPaste) == 0) {
                                        editor884250017.addListener('beforepaste', editorPaste);
                                    };
                                </script>
                                                                                    </div>
                    </div>
                    <div class="clear"></div>
                </div>

                            <input type="hidden" name="blankNum884250017" value="1,"/></div>									</form>

				   <!--解析-->
	   	<!--解析end-->

				</div>

				</div>
				
					<div class="nextDiv">
													 <a class="btnBlue btn_92 fs14" href="javascript:;" onClick="getTheNextQuestion(-1)"   >上一题</a>						
												 	<a href="javascript:;" class="jb_btn jb_btn_92 fs14" onClick="getTheNextQuestion(1)"  >下一题</a>
											</div>

				</div>
			<!--单选题 end-->
		</div>
		
    		</div>
	<div class="Marking_right_280" id="rightHeight" style="z-index:999;">
				<div class="foldBox">
              <a href="javascript:;" class="foldBtn" style="display: inline-block;"><i></i></a>
              <a href="javascript:;" class="unfoldBtn" style="display: none;">答题卡</a>
        </div>
				<div class="bomHet50">
        	<span class="dq"><i></i>当前题目</span>
        	<span class="yp"><i></i>已作答</span>
        	<span class="wp"><i></i>未作答</span>
        </div>
		<div class="topicNumber" id="topicNumberScroll" style="height:calc(100% - 60px);">
            																			<div class="topicNumber_checkbox colorDeep fs14"><span class="numRight fr"></span>一、 单选题 （30.0 分） </div>
													<ul class="topicNumber_list clearfix">
																			
																							<li  class=" active "   onclick="getTheQuestionByStart(0, '0');" >
							1
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(1, '0');" >
							2
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(2, '0');" >
							3
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(3, '0');" >
							4
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(4, '0');" >
							5
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(5, '0');" >
							6
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(6, '0');" >
							7
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(7, '0');" >
							8
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(8, '0');" >
							9
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(9, '0');" >
							10
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(10, '0');" >
							11
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(11, '0');" >
							12
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(12, '0');" >
							13
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(13, '0');" >
							14
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(14, '0');" >
							15
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(15, '0');" >
							16
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(16, '0');" >
							17
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(17, '0');" >
							18
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(18, '0');" >
							19
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(19, '0');" >
							20
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(20, '0');" >
							21
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(21, '0');" >
							22
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(22, '0');" >
							23
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(23, '0');" >
							24
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(24, '0');" >
							25
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(25, '0');" >
							26
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(26, '0');" >
							27
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(27, '0');" >
							28
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(28, '0');" >
							29
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(29, '0');" >
							30
						</li>
																				</ul>
																				<div class="topicNumber_checkbox colorDeep fs14"><span class="numRight fr"></span>二、 多选题 （25.0 分） </div>
													<ul class="topicNumber_list clearfix">
																			
																							<li  class=" active "   onclick="getTheQuestionByStart(30, '0');" >
							31
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(31, '0');" >
							32
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(32, '0');" >
							33
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(33, '0');" >
							34
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(34, '0');" >
							35
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(35, '0');" >
							36
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(36, '0');" >
							37
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(37, '0');" >
							38
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(38, '0');" >
							39
						</li>
																													<li  class=" active "   onclick="getTheQuestionByStart(39, '0');" >
							40
						</li>
																				</ul>
																				<div class="topicNumber_checkbox colorDeep fs14"><span class="numRight fr"></span>三、 填空题 （20.0 分） </div>
													<ul class="topicNumber_list clearfix">
																			
																							<li  class=" current "   onclick="getTheQuestionByStart(40, '0');" >
							41
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(41, '0');" >
							42
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(42, '0');" >
							43
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(43, '0');" >
							44
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(44, '0');" >
							45
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(45, '0');" >
							46
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(46, '0');" >
							47
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(47, '0');" >
							48
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(48, '0');" >
							49
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(49, '0');" >
							50
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(50, '0');" >
							51
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(51, '0');" >
							52
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(52, '0');" >
							53
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(53, '0');" >
							54
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(54, '0');" >
							55
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(55, '0');" >
							56
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(56, '0');" >
							57
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(57, '0');" >
							58
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(58, '0');" >
							59
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(59, '0');" >
							60
						</li>
																				</ul>
																				<div class="topicNumber_checkbox colorDeep fs14"><span class="numRight fr"></span>四、 判断题 （25.0 分） </div>
													<ul class="topicNumber_list clearfix">
																			
																							<li  class=""   onclick="getTheQuestionByStart(60, '0');" >
							61
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(61, '0');" >
							62
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(62, '0');" >
							63
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(63, '0');" >
							64
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(64, '0');" >
							65
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(65, '0');" >
							66
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(66, '0');" >
							67
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(67, '0');" >
							68
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(68, '0');" >
							69
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(69, '0');" >
							70
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(70, '0');" >
							71
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(71, '0');" >
							72
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(72, '0');" >
							73
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(73, '0');" >
							74
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(74, '0');" >
							75
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(75, '0');" >
							76
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(76, '0');" >
							77
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(77, '0');" >
							78
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(78, '0');" >
							79
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(79, '0');" >
							80
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(80, '0');" >
							81
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(81, '0');" >
							82
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(82, '0');" >
							83
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(83, '0');" >
							84
						</li>
																													<li  class=""   onclick="getTheQuestionByStart(84, '0');" >
							85
						</li>
																				</ul>
												</div>
	</div>

</div>



<div class="maskDiv" style="display:none;z-index:2001;"  id="timeOverSubmitConfirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">作答时间耗尽，试卷已提交</p>
			<p class="popWord fs16 colorIn" style="margin: 2px 2px;">试卷领取时间：2025-07-28 20:22</p>
			<p class="popWord fs16 colorIn" style="margin: 10px 2px;">考试用时：<span class="consumeMinutes">60</span>分钟</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv " style="display:none;z-index:2001;" id="showLoginInfo">
    <div class="popDiv liveEwmPop">
        <div class="popHead">
            <a href="#" class="popClose fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#showLoginInfo').fullFadeOut();"/></a>
            <p class="fl fs18 colorDeep">直播二维码</p>
        </div>
        <div class="het62"></div>
        <div class="livePop_con">
            <div class="fs16 color1 liveweminfo">
                <p>账号：<span class="color0" id="loginName"></span></p>
                <p>密码：<span class="color0" id="password"></span></p>
            </div>
            <dl class="pop_ewm">
                <dd>二维码仅考试期间有效</dd>
                <dt><img id="ewmUrl" src="" onclick="rereshSecondDeviceQRCode()" /></dt>
            </dl>
        </div>

    </div>
</div>

<div class="maskDiv" style="display:none;z-index:2000;"  id="submitConfirmPop" tabindex="0" role="alertdialog" aria-label="交卷" >
	<div class="popDiv wid440 Marking" style="left:39%;top:30%;width:450px;min-height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep" tabindex="0" role="option" >提示</p>
				<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭"
									>
					<img tabindex="-1" aria-hidden="true" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');">
				</a>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin: 30px 2px;" tabindex="0" role="option"  >确认交卷？</p>

			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick=""
									>确定</a>
				<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#submitConfirmPop').fullFadeOut();$('#showNextPop').val('false');"
									>取消</a>
			</div>
			<div class="het72" tabindex="0" role="option"  aria-label="弹窗结尾" ></div>
				</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;"  id="audioLimitTimesWin">
	<div class="popSetDiv wid440">
			<div class="popHead RadisTop">
				<a href="javascript:;" class="popClose fr" onclick="$('#audioLimitTimesWin').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 color1">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 color2 audioLimitTimesTip">此附件仅支持打开 <span></span> 次，你已打开 <span></span> 次，不能再次打开</p>
			<div class="popBottom RadisBom">
				<a href="javascript:;" class="jb_btn jb_btn_92 fr fs14" onclick="$('#audioLimitTimesWin').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv popMoveShowHide" id="confirmEnterWin" style="display:none;z-index:1000;" tabindex="0" role="alertdialog" aria-label="进入考试"
	 >
	<div class="popDiv wid440 popMove">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr" tabindex="0" role="button" aria-label="关闭"
				>
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmEnterWin').fullFadeOut();"/>
			</a>
			<p class="fl fs18 colorDeep" style="font-size:18px;" tabindex="-1"  >提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option" >
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 140px;width:100%;"></div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" id="tabIntoexam2" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">进入考试</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;"
							>取消</a>
		</div>
		<div class="het72" tabindex="0" role="option" id="confirmEnterWinEnd" aria-label="弹窗结尾"  ></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;"  id="multiTerminalWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:300px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:18px 2px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">继续考试</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#confirmEnterWin').fullFadeOut();" style="width:88px;">退出考试</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;"  id="examTipsPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examTipsPop').fullFadeOut();">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="singleQuesLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"> 本题作答时间已用完，将进入下一题 </div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="singleQuesLimitTimeConfirm();"> 确定 </a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="groupPaperLimitTimePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
			 当前考试需按分卷顺序作答，确认进入下一个分卷？ </div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
			<a href="javascript:" class="btnBlue btn_92_cancel fr fs14" onclick="$('#groupPaperLimitTimePop').fullFadeOut();$('#groupStart').val(0)">取消</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="groupPaperLimitTimeOverPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">
							当前分卷作答时间已用完，将进入下一分卷					</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="groupPaperLimitTimeSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:200px">
		<div class="popHead">
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;">当前分卷需限时耗尽才允许提交</div>
		<div class="popBottom">
			<a href="javascript:" class="jb_btn jb_btn_92 fr fs14" onclick="groupLimitTimeConfirm();">确定</a>
		</div>
		<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="switchScreenPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;position:relative;top:20px;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#switchScreenPop').fullFadeOut();">确定</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv popMoveShowHide" id="confirmRetestWin" style="display:none;z-index:1000;" tabindex="0" role="alertdialog" aria-label="重考提示"
	  >
	<div class="popDiv wid440 popMove">
		<div class="popHead">
			<a href="javascript:;" class="popClose fr"  tabindex="0" role="button" aria-label="关闭"
				>
				<img tabindex="-1" src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" onclick="$('#confirmRetestWin').fullFadeOut();"/></a>
			<p class="fl fs18 colorDeep" style="font-size:18px;" >提示</p>
		</div>
		<div class="het62"></div>
		<div class="readPad" style="padding-bottom:0px;" tabindex="0" role="option" >
			<div class=" tip" style="line-height:26px;font-size:16px;min-height: 80px;width:100%;">开始重考后，请重新提交考试，否则可能影响最终成绩。本次考试教师已设置取最后一次考试成绩为最终成绩，请确认是否重考？</div>
		</div>
		<div class="popBottom">
			<a tabindex="0" role="button" href="javascript:;" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">确定重考</a>
			<a tabindex="0" role="button" href="javascript:;" class="btnBlue btn_92_cancel fr fs14" onclick="$('#confirmRetestWin').fullFadeOut();" style="width:88px;"
							>取消</a>
		</div>
					<div class="het72"></div>
			</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;"  id="exitTimesSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你已离开考试{1}次，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitTimesSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="exitDurationSubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;" data="系统检测到你的切屏时长已超过{1}秒，将强制收卷"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitDurationSubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:2001;"  id="teacherNoticePop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:300px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">教师提醒</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 10px 2px; height:200px;overflow:auto;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#teacherNoticePop').fullFadeOut()">知道了</a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv taskStatusShowHide" style="display:none;z-index:1000;" id="stuSelfTestAutoPaper">
	<div class="popDiv wid440 centered">
		<div class="popHead">
			<a href="javascript:" class="popClose popMoveDele fr"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
			<p class="fl fs18 colorDeep">提示</p>
		</div>
		<div class="het62"></div>
		<div class="barDiv">
			<div class="barCon" style="width:10%"></div>
		</div>
		<p class="barInfo" style="margin-bottom:20px">自测试卷生成中，已完成<span id="taskrate">0%</span></p>
		<div class="popBottom">
								</div>
		<div class="het72"></div>
	</div>
</div>



<div class="maskDiv"  style="display:none;z-index:1000;"  id="faceRecognitionComparePop">
	<div class="popDiv iden_resultPop">
        		<div class="popHead">
        			<a href="javascript:" class="popClose fr" onclick="$('#faceRecognitionComparePop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
        			<p class="fl fs18 colorDeep">识别结果</p>
        		</div>
        		<div class="het62"></div>
                <div class="face_Box face_compared">
                    <p class="contrastTit textCenter face_result"><i class="icon"></i><span class="tip"></span></p>
                    <div class="contrastImgBox textCenter">
                        <dl>
                            <dt><img src="" class="currentFaceId"></dt>
                            <dd>本次采集</dd>
                        </dl>
                        <dl class="greenDL">
                            <dt><img src="" class="collectedFaceId"></dt>
                            <dd class="archivePhoto">档案照片</dd>
                        </dl>
                    </div>
                    <div class="face_video_btn textCenter marTop60 face_fail_actionbtn">
                                                <a href="javascript:" class="disble_time_btn jb_btn_158 fs14 reFaceRecognition">重新识别</a>
                    </div>
                </div>
				<div class="face_Box face_comparing">
                    <p class="contrast_loading"><i><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/load.png"></i>人脸比对中...</p>
                </div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;"  id="forceSubmitTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;"></div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#forceSubmitTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>



<div class="maskDiv" style="display:none;z-index:1000;"  id="examAddTimeRemindTip">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;max-height:360px;">
			<div class="popHead"><p class="fl fs18 colorDeep">延时提醒</p></div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;">该考试教师进行延时操作</p>
			<p class="popWord fs16 colorIn minutes" style="margin:2px 2px;" data="延时时长：[1]分钟"></p>
			<p class="popWord fs16 colorIn remind" style="margin:6px 2px;overflow: auto;max-height:160px;word-break: break-all;" data="延时原因：[1]"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#examAddTimeRemindTip').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;"  id="confirmPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#confirmPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">删除后将无法恢复，确认删除？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmDelete" onclick="">确定</a>
				<a href="javascript:" onclick="$('#confirmPop').fullFadeOut();" class="btnBlue btn_92_cancel fr fs14">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;"  id="examWrongQuesPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;">
			<div class="popHead">
				<a href="javascript:" class="popClose fr" onclick="$('#examWrongQuesPop').fullFadeOut();"><img src="//mooc1.chaoxing.com/exam-ans/mooc2/images/popClose.png" /></a>
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn deleteRecoverTip">您有正在进行的错题练习，是否继续上次作答？</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 continueAnswer" onclick="">继续作答</a>
				<a href="javascript:" onclick="" class="btnBlue btn_92_cancel fr fs14 reAnswer">重刷</a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<div class="maskDiv" style="display:none;z-index:1000;"  id="lockExamWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;"></p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="">申诉</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="" style="width:88px;">退出考试</a>			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="exitexamForcesubmitWin">
	<div class="popDiv wid440 Marking" style="left:50%;top:40%;width:480px;height:220px;margin-left:-240px;">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<p class="popWord fs16 colorIn" style="margin:6px 2px;padding:26px 30px;">退出考试将强制收卷，确认退出?</p>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirm" onclick="">退出</a>
				<a href="javascript:;" class="btnBlue btn_92_cancel fr fs14 cancel" onclick="$('#exitexamForcesubmitWin').fullFadeOut();" style="width:88px;">取消</a>
			</div>
			<div class="het72"></div>
	</div>
</div>

<div class="maskDiv" style="display:none;z-index:1000;"  id="exitexamForcesubmitPop">
	<div class="popDiv wid440 Marking" style="left:39%;top:40%;width:480px;height:220px">
			<div class="popHead">
				<p class="fl fs18 colorDeep">提示</p>
			</div>
			<div class="het62"></div>
			<div class="popWord fs16 colorIn" style="margin: 6px 2px;overflow: auto;height:160px;word-break: break-all;">系统检测到曾退出考试，将强制收卷</div>
			<div class="popBottom">
				<a href="javascript:" class="jb_btn jb_btn_92 fr fs14 confirmClose" onclick="$('#exitexamForcesubmitPop').fullFadeOut();" data="知道了"></a>
			</div>
			<div class="het72"></div>
	</div>
</div>


<script>
	$(document).keydown(function(event){
		if ((event.altKey)&& ((event.keyCode==37)|| (event.keyCode==39))) {
			event.returnValue=false;
			return false;
		}

		if(event.keyCode==116){
			return false;
		}

		if((event.ctrlKey) && (event.keyCode==82)){
			return false;
		}
		if(event.keyCode==13) {
			if (typeof (event.target) != "undefined" && event.target != null) {
				if (!$(event.target).hasClass("canEnterType")) {
					event.preventDefault();
				}
			} else {
				//屏蔽回车提交
				event.preventDefault();
			}
		}
	});

	var maxSecond = 59;
	maxtime = 2645;
	var limitTime = 60 * 60;
	var testPaperStatus = "1";

	if(maxtime >= 0) {
		timers = setInterval("CountDown()",1000);
		function CountDown(){
			if(maxtime>0){
				minutes = Math.floor(maxtime/60);
				if(minutes < 10) {
					minutes = "0" + minutes;
				}
				seconds = Math.floor(maxtime%60);
				if(seconds < 10) {
					seconds = "0" + seconds;
				}
				msg = minutes +"' "+seconds+"''";

				//$(".mark_timeDiv").show();
				$("#timer").html( msg);
				$("#remainTime").val(maxtime);
				--maxtime;
			} else{
				$("#timeOver").val(true);
				clearInterval(timers);
				timeOverSubmitTest();
			}
		}
	}

	$(document).ready(function () {
    	var webSnapshotMonitor = $('#webSnapshotMonitor').val();
    	var snapshotMonitor = $('#snapshotMonitor').val();
    	if (snapshotMonitor == 1 && webSnapshotMonitor != 1) {
    		var entryExamTime = $('#entryExamTime').val();
    		var rentryExamTime = $('#rentryExamTime').val();
    		var data = {};
    		try{
        		var faceDetectionResult = $('#faceDetectionResult').val();
        		if (faceDetectionResult && faceDetectionResult != '') {
        			data = JSON.parse(faceDetectionResult);
        		}
				if($('#electronSnapshotMonitor').val() == 1){
				    data = loadElectronEnvAndFace() || data;
				}
    		}catch(err){}
    		if (entryExamTime && entryExamTime > 0) {
    			data.entryTime = entryExamTime;
    			entryExamLog(data);
    		} else if (rentryExamTime && rentryExamTime > 0) {
    			data.rentryTime = rentryExamTime;
    			rentryExamLog(data);
    		}
    	    // openExamClientFaceMonitor('-1', '0', '{"classId":"*********", "answerId":"*********"}') ;
			openExamClientScreenAndCapture('-1', '0', '{"classId":"*********", "answerId":"*********"}');
    	}
    	var pcclientSwitchout = $('#pcclientSwitchout').val();
    	if(pcclientSwitchout == 1 && webSnapshotMonitor != 1){
    	    openExamClientScreenCutting();
    	}else if(pcclientSwitchout == 0 && webSnapshotMonitor != 1){
		    closeExamClientScreenCutting();
		}
		checkExamClientStartScreencap();
	});

	function getTheNextQuestion(n){
		var unAnswer = checkQuestionAnswer(n);
		if (unAnswer) {
			return;
		}
		submitForm(true, false, function(data) {
			var array=data.split("|");
			var s=40+n;
			var lastUpdateTime=array[0];
			var remainTime=array[1];
			var enc=array[2];
			var url =_HOST_CP2_ + "/exam/test/reVersionTestStartNew?keyboardDisplayRequiresUserAction=1&getTheNextQuestion=1&courseId=*********&classId=*********"+
					"&tId=7689933&id=*********&p=1&start="+s+ "&remainTimeParam="+remainTime+"&relationAnswerLastUpdateTime="+lastUpdateTime+"&enc="+enc
					+"&monitorStatus=0&monitorOp=-1&examsystem=0&qbanksystem=0&qbankbackurl=&cpi=*********&openc=32b95692130f772c1f237eb6146cf0bc&newMooc=true&webSnapshotMonitor=0";
			if($("#isChaoxingExamPc").val() == 'true'){
				url +=  '&sc=' + $('#screenAndCaptureCall').val();
			}
			window.location.href = url;
		});
	}

	function getTheQuestionByStart(n, targetGroupId){
		var noPass = groupPaperLimitTimeNextCheck(n, targetGroupId);
		if (noPass) {
			return;
		}
		submitForm(true, false, function(data) {
			var array=data.split("|");
			var lastUpdateTime=array[0];
			var remainTime=array[1];
			var enc=array[2];
			var url = _HOST_CP2_ + "/exam/test/reVersionTestStartNew?keyboardDisplayRequiresUserAction=1&courseId=*********&classId=*********&tId=7689933&id=*********&p=1&start="+n+
					"&monitorStatus=0&monitorOp=-1&examsystem=0&qbanksystem=0&qbankbackurl=&remainTimeParam="+remainTime+"&relationAnswerLastUpdateTime="+lastUpdateTime+"&enc="+enc+"&cpi=*********&openc=32b95692130f772c1f237eb6146cf0bc&newMooc=true&webSnapshotMonitor=0";
			if($("#isChaoxingExamPc").val() == 'true'){
				url +=  '&sc=' + $('#screenAndCaptureCall').val();
			}
			window.location.href = url;
		});
	}

	$(function(){
	
	    checkStartSingleQuesLimitTimeTimer();
		checkStartGroupPaperLimitTimeTimer();
		checkBrowerBack();
		
	    $(".optionsCon").niceScroll({
			cursorborder : "",
			cursorwidth : "8px",
			cursorcolor : "#E6ECF5",
			boxzoom : false
		});
		$('textarea').autoHeight({len:53});
		answerCardLocateCurrent();

		 $(document).ready(function() {
			$(".programAswer").niceScroll({cursorborder:"",cursorwidth:"8px", cursorcolor:"#E6ECF5",boxzoom:false});
			$(".optionsCon").niceScroll({cursorborder:"", cursorwidth:"8px", cursorcolor:"#E6ECF5",boxzoom:false});
		  });
		if("0" == "1"){
			// 禁止读取非第一题考试左侧边栏内容
			if("40" && "40" > 0){
				if ($("#tabMarkingLeft") && $("#tabMarkingLeft").find("*")){
					$("#tabMarkingLeft").find("*").attr({tabindex:"-1", "aria-hidden":true});
				}
			}
			var firstFocus = $(".subNav");
			if (firstFocus) {
				setTimeout(function() {
					try {
						if (window.top && window.top.accessiblePlugs) {
							window.top.accessiblePlugs.update();
						}
					} catch (e) {
						console.log(e)
					}
					firstFocus.eq(0).focus();
				}, 300)
			}
		}
	});
		
	$(document).bind("visibilitychange",function(e){
         checkRemainTime(document.visibilityState);
    });
</script>
<script src="//mooc1.chaoxing.com/exam-ans/space/work/js/preview-attach.js?v=2023-1102-1900"></script><link href="//mooc1.chaoxing.com/exam-ans/css/work/viewer.min.css?v=2021-0830-1700" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/js/jquery.md5.js"></script>
<script type="text/javascript" src="//mooc1.chaoxing.com/exam-ans/space/work/js/viewer-jquery.min.js?v=2024-0124-0200"></script>
<script type="text/javascript">
try{
	$(function(){
		var imgList = $(".TiMu").find("div img:not(.workAttach img, .attach img, .attachNew img, .stuAnswerArea img, .popClose img, .ans-formula-moudle, .no_view)");
		for (var i = 0, len = imgList.size(); i < len; i++) {
			 var src = imgList.eq(i).attr("src");
			 if(src){
    			var index = src.indexOf("375_1024");
    			if (index != -1) {
    				src = src.replace("375_1024", "origin");
    			}
                 var index2 = src.indexOf("750_1024");
                 if (index2 != -1) {
                     src = src.replace("750_1024", "origin");
                 }
    			imgList.eq(i).attr("data-original", src);
		   }
		}
        $(".TiMu").find("div img:not(.workAttach img, .attach img, .attachNew img, .stuAnswerArea img, .popClose img, .no_view)").viewer({
			url : 'data-original',
		});
	})
  }catch(error){}
</script><script src="//mooc1.chaoxing.com/exam-ans/mooc2/js/exam/watermark.min.js?v=2023-0131-0946"></script>
<!-- 代码高亮开始 start -->
<link type="text/css" href="//mooc1.chaoxing.com/exam-ans/views/mooc2/ueditor/insertcode/css/default.min.css" rel="stylesheet" />
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/highlight.min.js" charset="utf-8"></script>
<script type="text/javascript" src="//mooc-res2.chaoxing.com/exam-ans/js/highlightjs-line-numbers.js" charset="utf-8"></script>
<style>
    pre,code {display: block;background: #F7F8FA;border: 1px solid #E1E3E5; font-size: 14px; border-radius: 4px; padding: 30px 20px; margin: 20px 0; font-family: CXHackSafariFont,CXEmojiFont,CXChineseQuote,-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Tahoma, Arial,Segoe UI,PingFang SC, Hiragino Sans GB,Microsoft YaHei,sans-serif,Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,Noto Color Emoji; }
    pre{overflow-x: auto;margin:.5em 0;padding:.4em .6em;border-radius:8px;background:#f8f8f8;}
    pre.autolinebreak{white-space: normal; }
    code.hljs{background: #F7F8FA;}
    pre code,pre pre{ margin: 0; background: none;border: none;}/*padding: 0;*/
    .code-tool-wrap{padding-bottom: 1em;left: 20px; top: 2px; width: calc(100% - 40px); height: 24px; font-size: 14px; user-select: none;}
    .code-lang{ display:inline-block; color: #ACB4BF; }
    .ans-cc .ans-noborder th,.ans-cc .ans-noborder td{border-color:transparent;}
    .ans-cc pre,.ans-cc code{ position: relative; padding: 0px 20px 30px; margin: 20px 0;border: 1px solid #E1E3E5; border-radius: 4px; color: #474C59; background: #F7F8FA; font-size:14px; font-family: CXHackSafariFont,CXEmojiFont,CXChineseQuote,-apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Tahoma, Arial,Segoe UI,PingFang SC, Hiragino Sans GB,Microsoft YaHei,sans-serif,Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol,Noto Color Emoji;  }
    .ans-cc pre{overflow: auto}
    .ans-cc pre code,.ans-cc pre pre{ padding: 0; border:none; margin: 0; background: none;border-radius: 0;}
    .ans-cc pre.autolinebreak{white-space: pre-wrap;word-wrap: break-word;}
    .hljs-ln-numbers {text-align: center;color: #ccc;border-right: 1px solid #CCC;vertical-align: top;padding-right: 5px !important;}
    .hljs-ln-code {padding-left: 5px !important;}
    /* 全局去掉表格样式 */
    .ans-cc pre code table tr td {border: solid 0px #cfd4dc;text-align: left}
    /* 作业表格适配，考试表格适配 */
    .ans-cc pre table{table-layout: unset;}
    .ans-cc pre code table{table-layout: unset;}
    /* pre 标签未换行 */
    .ans-cc pre.line-numbers{clear:both}
    .ans-cc pre{clear:both}

    /* app作业库 考试库 查看样式 */

    .hljs-link, .hljs-operator, .hljs-regexp, .hljs-selector-attr, .hljs-selector-pseudo, .hljs-symbol, .hljs-template-variable, .hljs-variable {
        color: #ab5656 !important;
    }
    .hljs-deletion, .hljs-number, .hljs-quote, .hljs-selector-class, .hljs-selector-id, .hljs-string, .hljs-template-tag, .hljs-type {
        color: #800 !important;
    }
    /* 章节作业 */
    .hljs-ln span{margin-right:0}

    /* 学习通颜色覆盖 */
    .hljs-type{color:#800 !important;}
    .hljs-section,.hljs-title{color:#800 !important;font-weight:700}
    /* 背景颜色 */
    .ans-cc pre code td{background-color: unset;}
</style>
<link type="text/css" href="//mooc1.chaoxing.com/exam-ans/css/newEditor.css?v=2023-0609-1900" rel="stylesheet" />
<script>
    /**
     * 保证最后加载
     */
    $(function () {
        var pre = $(".ans-cc").find('pre')
        if(pre){
            pre.each(function () {
                // debugger
                var me = this;
                if ($(me).hasClass('CodeMirror-line')) { //代码编辑器
                    return;
                }
                var lang = me.firstChild && me.firstChild.lang;
                if (typeof (lang) == 'undefined' || lang == null || lang == "" ) {
                    lang = me.firstChild && me.firstChild.className;
                    if (typeof (lang) != 'undefined' && lang != null && lang != "" ) {
                        var split = lang.split("-");
                        lang = split[1];
                        if (lang == 'plain') {
                            lang = 'Plain Text';
                        }
                    } else {
                        lang = 'Plain Text';
                    }
                }
                me.innerHTML = '<div class="code-tool-wrap clearfix"><div class="code-lang fl">' + lang
                        + '</div><div class="copy1 fr"><i></i><span></span></div></div>' + me.innerHTML;
                me.innerHTML = me.innerHTML.replace(/<br\s*\/?>/g, "\n");
            })
        }

        try {
            hljs.initHighlightingOnLoad();
            hljs.initLineNumbersOnLoad({ singleLine:true });
        } catch (e) {

        }
    })
</script>
<!-- 代码高亮结束 end -->
	<script  type="text/javascript" async="async" defer="defer" src="https://detect.chaoxing.com/api/passport2-onlineinfo.js?key=true&refer=http://i.mooc.chaoxing.com&from=exam&callback=ture&_v=1753706303998-11701"></script>
 
</body>
</html>