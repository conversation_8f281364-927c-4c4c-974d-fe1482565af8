2025-07-27 22:51:46.464 | INFO     | __main__:<module>:1894 - 学习通自动化系统启动...
2025-07-27 22:51:46.464 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-27 22:51:46.464 | SUCCESS  | __main__:<module>:1904 - 数据库连接池初始化成功
2025-07-27 22:51:46.466 | SUCCESS  | __main__:<module>:1925 - 数据状态重置完成
2025-07-27 22:51:46.466 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 0/100
2025-07-27 22:51:46.466 | INFO     | __main__:<module>:1941 - 开始处理订单...
2025-07-27 22:51:46.467 | INFO     | __main__:order_get:1697 - 订单处理线程启动，最大线程数: 100
2025-07-27 22:51:46.468 | INFO     | __main__:order_get:1722 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-27 22:51:46.468 | INFO     | __main__:Run:1781 - 开始处理订单: OID=300000, 用户=17794224040, 课程ID=245544539|104443004|418796817|40107501|55319552|6e7ca19cc8b85c82b5e9ed4c57089d04
2025-07-27 22:51:46.468 | INFO     | __main__:register_thread:67 - 注册线程: OID=300000, 当前活跃线程数: 1
2025-07-27 22:51:46.985 | SUCCESS  | __main__:Run:1825 - ID:17794224040,登录成功
2025-07-27 22:51:46.993 | INFO     | __main__:kclist:225 - ID:17794224040,平台ID 9004，使用特殊处理方式获取作业信息
2025-07-27 22:51:46.994 | SUCCESS  | __main__:kclist:242 - ID:17794224040,成功构造作业课程信息: {'kcname': '作业任务', 'courseid': '245544539|104443004|418796817|40107501|55319552|6e7ca19cc8b85c82b5e9ed4c57089d04', 'clazzid': '245544539|104443004|418796817|40107501|55319552|6e7ca19cc8b85c82b5e9ed4c57089d04', 'cpi': ''}
2025-07-27 22:51:46.994 | SUCCESS  | __main__:Run:1834 - ID:17794224040,课程信息匹配成功
2025-07-27 22:51:46.996 | SUCCESS  | __main__:studentstudy:935 - ID:17794224040,进度:100%,详情:作业任务_40107501 | 进行中 | 进度: 1/1 | 更新: 2025-07-27 22:51:46
2025-07-27 22:51:48.193 | INFO     | __main__:_fallback_to_traditional_homework:1501 - ID:17794224040,获取到真实作业标题: 第一单元课文课外练习10道
2025-07-27 22:51:48.744 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1394 - ID:17794224040,提取到基本表单字段: courseId=245544539, classId=104443004, workId=40107501
2025-07-27 22:51:48.746 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1405 - ID:17794224040,找到 10 个题目项
2025-07-27 22:51:48.749 | INFO     | API.HomeworkAI:_extract_form_data_and_questions:1535 - ID:17794224040,成功提取到 10 个问题
2025-07-27 22:51:48.749 | ERROR    | API.HomeworkAI:process_homework:1025 - ID:17794224040,未生成任何答案
2025-07-27 22:51:49.413 | INFO     | API.WorkTask:Html_Wkrk:188 - ID:17794224040,尝试解析作业页面参数
2025-07-27 22:52:01.792 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: downtown
2025-07-27 22:52:01.793 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Adowntown', 'B': 'Bcommute', 'C': 'Ctransportation', 'D': 'Ddirection'}
2025-07-27 22:52:01.794 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: downtown
2025-07-27 22:52:01.794 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: A
2025-07-27 22:52:01.794 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 1/10 个题目
2025-07-27 22:52:15.068 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: transportation
2025-07-27 22:52:15.070 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Adowntown', 'B': 'Bfavor', 'C': 'Cintersection', 'D': 'Dtransportation'}
2025-07-27 22:52:15.070 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: transportation
2025-07-27 22:52:15.070 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: D
2025-07-27 22:52:15.071 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 2/10 个题目
2025-07-27 22:52:15.423 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Atraffic', 'B': 'Bcommute', 'C': 'Cpollution', 'D': 'Ddirection'}
2025-07-27 22:52:15.424 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: pollution
2025-07-27 22:52:15.425 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: C
2025-07-27 22:52:15.425 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:17794224040,主题库答案匹配成功: C
2025-07-27 22:52:15.426 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:17794224040,已处理 3/10 个题目
2025-07-27 22:52:27.554 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: figure out
2025-07-27 22:52:27.555 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Atowards', 'B': 'Bblock', 'C': 'Conabudget', 'D': 'Dfigureout'}
2025-07-27 22:52:27.556 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: figure out
2025-07-27 22:52:27.557 | INFO     | API.WorkTask:Xuan:1159 - ID:17794224040,尝试使用相似度匹配
2025-07-27 22:52:27.558 | INFO     | API.WorkTask:Xuan:1179 - ID:17794224040,尝试使用关键词匹配，答案关键词: {'out', 'figure'}
2025-07-27 22:52:27.562 | INFO     | API.WorkTask:Xuan:1228 - ID:17794224040,尝试使用短语匹配，答案短语: ['figure out']
2025-07-27 22:52:27.562 | ERROR    | API.WorkTask:Xuan:1336 - ID:17794224040,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 22:52:27.562 | WARNING  | API.WorkTask:Xuan:1340 - ID:17794224040,答案无法与任何选项匹配
2025-07-27 22:52:27.563 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 4/10 个题目
2025-07-27 22:52:40.090 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: downtown
2025-07-27 22:52:40.094 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Adowntown', 'B': 'Bintersection', 'C': 'Ccommute', 'D': 'Ddirection'}
2025-07-27 22:52:40.095 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: downtown
2025-07-27 22:52:40.096 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: A
2025-07-27 22:52:40.097 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 5/10 个题目
2025-07-27 22:52:46.468 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 22:52:52.450 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: pollution
2025-07-27 22:52:52.451 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Atraffic', 'B': 'Bcommute', 'C': 'Cpollution', 'D': 'Ddirection'}
2025-07-27 22:52:52.452 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: pollution
2025-07-27 22:52:52.452 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: C
2025-07-27 22:52:52.453 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 6/10 个题目
2025-07-27 22:53:05.463 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: on a budget
2025-07-27 22:53:05.465 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Atowards', 'B': 'Bblock', 'C': 'Conabudget', 'D': 'Dfigureout'}
2025-07-27 22:53:05.466 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: on a budget
2025-07-27 22:53:05.467 | INFO     | API.WorkTask:Xuan:1159 - ID:17794224040,尝试使用相似度匹配
2025-07-27 22:53:05.468 | INFO     | API.WorkTask:Xuan:1179 - ID:17794224040,尝试使用关键词匹配，答案关键词: {'on', 'a', 'budget'}
2025-07-27 22:53:05.474 | INFO     | API.WorkTask:Xuan:1228 - ID:17794224040,尝试使用短语匹配，答案短语: ['on a budget']
2025-07-27 22:53:05.474 | ERROR    | API.WorkTask:Xuan:1336 - ID:17794224040,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 22:53:05.474 | WARNING  | API.WorkTask:Xuan:1340 - ID:17794224040,答案无法与任何选项匹配
2025-07-27 22:53:05.474 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 7/10 个题目
2025-07-27 22:53:17.659 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: downtown
2025-07-27 22:53:17.663 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Adowntown', 'B': 'Bdirections', 'C': 'Cintersections', 'D': 'Dtransportation'}
2025-07-27 22:53:17.664 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: downtown
2025-07-27 22:53:17.665 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: A
2025-07-27 22:53:17.665 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 8/10 个题目
2025-07-27 22:53:29.646 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: as if
2025-07-27 22:53:29.648 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Aincase', 'B': 'Bgetoff', 'C': 'Csofar', 'D': 'Dasif'}
2025-07-27 22:53:29.648 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: as if
2025-07-27 22:53:29.649 | INFO     | API.WorkTask:Xuan:1159 - ID:17794224040,尝试使用相似度匹配
2025-07-27 22:53:29.651 | INFO     | API.WorkTask:Xuan:1179 - ID:17794224040,尝试使用关键词匹配，答案关键词: {'if', 'as'}
2025-07-27 22:53:29.654 | INFO     | API.WorkTask:Xuan:1228 - ID:17794224040,尝试使用短语匹配，答案短语: ['as if']
2025-07-27 22:53:29.655 | ERROR    | API.WorkTask:Xuan:1336 - ID:17794224040,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 22:53:29.655 | WARNING  | API.WorkTask:Xuan:1340 - ID:17794224040,答案无法与任何选项匹配
2025-07-27 22:53:29.656 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 9/10 个题目
2025-07-27 22:53:41.820 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:17794224040,AI返回的答案内容: harvest
2025-07-27 22:53:41.822 | INFO     | API.WorkTask:Xuan:989 - ID:17794224040,找到选项: {'A': 'Aprefer', 'B': 'Bdressup', 'C': 'Charvest', 'D': 'Dfigureout'}
2025-07-27 22:53:41.822 | INFO     | API.WorkTask:Xuan:990 - ID:17794224040,题库答案: harvest
2025-07-27 22:53:41.823 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:17794224040,答案与选项内容包含匹配: C
2025-07-27 22:53:41.823 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:17794224040,已处理 10/10 个题目
2025-07-27 22:53:41.823 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:17794224040,所有题目处理完成，共 10/10 个
2025-07-27 22:53:41.825 | INFO     | API.WorkTask:PostDo:1402 - ID:17794224040,AI答题比例: 90.00%, AI答题数量: 9, 题库答题比例: 10.00%, 题库答题数量: 1, 总题目数量: 10
2025-07-27 22:53:41.825 | INFO     | API.WorkTask:PostDo:1427 - ID:17794224040,题库答题比例:10.00%, 使用保存模式
2025-07-27 22:53:42.569 | SUCCESS  | API.WorkTask:PostDo:1471 - ID:17794224040,保存答案成功: {"msg":"保存成功！","status":true}
2025-07-27 22:53:42.573 | INFO     | API.WorkTask:_update_progress_info:1559 - ID:17794224040,尝试导入data.Porgres模块
2025-07-27 22:53:42.573 | INFO     | API.WorkTask:_update_progress_info:1565 - ID:17794224040,成功导入data.Porgres.StartProces
2025-07-27 22:53:42.574 | INFO     | API.WorkTask:_update_progress_info:1587 - ID:17794224040,创建StartProces对象
2025-07-27 22:53:42.575 | INFO     | API.WorkTask:_update_progress_info:1600 - ID:17794224040,调用get_platform_9004_progress方法
2025-07-27 22:53:42.575 | INFO     | API.WorkTask:_update_progress_info:1612 - ID:17794224040,进度更新: 100%, 第一单元课文课外练习10道 (已保存) | 题库答题比例:10% | AI答题比例:90%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-27 22:53:42 | 题库答题比例:10% | AI答题比例:90%，使用保存模式
2025-07-27 22:53:42.575 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-27 22:53:42.576 | INFO     | API.WorkTask:_update_progress_info:1620 - ID:17794224040,更新数据库记录, oid: 300000
2025-07-27 22:53:42.587 | SUCCESS  | __main__:_fallback_to_traditional_homework:1617 - ID:17794224040,进度:100%,详情:第一单元课文课外练习10道 | 题库答题比例:10% | AI答题比例:90%，使用保存模式 | 进度: 1/1 | 更新: 2025-07-27 22:53:42
2025-07-27 22:53:42.588 | SUCCESS  | __main__:_fallback_to_traditional_homework:1632 - ID:17794224040,传统方法作业任务 第一单元课文课外练习10道 处理成功
2025-07-27 22:53:42.588 | INFO     | __main__:studentstudy:1050 - ID:17794224040,作业任务 作业任务_40107501 完成，等待 8 秒后处理下一任务
2025-07-27 22:53:46.468 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 22:53:50.597 | INFO     | __main__:unregister_thread:74 - 注销线程: OID=300000, 当前活跃线程数: 0
2025-07-27 22:54:46.469 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 0/100
2025-07-27 22:55:09.550 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-27 22:55:09.550 | INFO     | __main__:<module>:1951 - 程序退出
