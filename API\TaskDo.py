import traceback
from API.WorkTask import StaratWorkTaks
from API.VideoTask import VideoStart
from loguru import logger
import string
import time
import parsel
import re
import random
import urllib3
import json
import requests
from bs4 import BeautifulSoup

# 尝试导入HomeworkAI模块
try:
    from API.HomeworkAI import HomeworkAI

    HOMEWORK_AI_AVAILABLE = True
except ImportError:
    HOMEWORK_AI_AVAILABLE = False
    logger.warning("HomeworkAI模块未找到，AI自动填充功能不可用")


class UA:
    def __init__(self):
        self.WEB = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.36"
        self.APP = {
            "User-Agent": f"/2.1.0 (Linux; U; Android 7.1.2; SM-G977N Build/LMY48Z) com.chaoxing.mobile/ChaoXingStudy_3_4.3.4_android_phone_494_27 (@Kalimdor)_{''.join(random.choice(string.hexdigits[:-6]) for _ in range(32))}"
        }


def random_uuid():
    hex_digits = "0123456789abcdef"
    s = []
    for i in range(36):
        if i in [8, 13, 18, 23]:
            s.append("-")
        else:
            s.append(random.choice(hex_digits))
    s[14] = "4"
    s[19] = random.choice(hex_digits[:4])
    s[19] = hex_digits[int(s[19], 16) & 0x3 | 0x8]
    uuid = "".join(s)
    return uuid


class Task:
    def __init__(self, session, KcList, attachment, defaults, chapterId, username):
        self.session = session
        self.username = username
        self.Uid = self.session.Uid()
        self.list_info = KcList
        self.courseid = KcList[0]["courseid"]
        self.clazzid = KcList[0]["clazzid"]
        self.kcname = KcList[0]["kcname"]
        self.cpi = KcList[0]["cpi"]
        self.attachment = attachment
        self.defaults = defaults
        self.chapterId = chapterId

    def task(self):
        # 判断是否需要减少日志输出
        reduce_logs = hasattr(self.session, "cid") and self.session.cid == 9004

        # 平台ID 9004 特殊处理 - 直接处理作业页面
        if hasattr(self, "is_assignment_task") and self.is_assignment_task:
            try:
                # 仅在非9004平台ID时输出详细日志
                if not reduce_logs:
                    logger.success(f"ID:{self.username},开始处理作业任务")

                # 构建作业页面URL
                api = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork"
                params = {
                    "courseId": self.courseid,
                    "classId": self.clazzid,
                    "cpi": self.cpi,
                    "workId": self.workid,
                    "answerId": self.answerId,
                    "enc": self.enc,
                }

                # 获取作业页面内容
                r = self.session.get(
                    api, params=params, headers=UA().WEB, allow_redirects=True
                )
                if r.status_code == 200:
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.success(f"ID:{self.username},成功获取作业页面")

                    # 使用与章节测验相同的答题处理逻辑
                    html_work = StaratWorkTaks(
                        self.session,
                        self.courseid,
                        self.clazzid,
                        self.cpi,
                        self.chapterId,
                        self.answerId,
                        self.kcname,
                        self.username,
                    )
                    html_work.Html_Wkrk(r.text)
                else:
                    # 仅在非9004平台ID时输出错误日志
                    if not reduce_logs:
                        logger.error(
                            f"ID:{self.username},获取作业页面失败，状态码:{r.status_code}"
                        )
                return
            except Exception as e:
                # 仅在非9004平台ID时输出错误日志
                if not reduce_logs:
                    logger.error(f"ID:{self.username},处理作业异常: {str(e)}")
                    traceback.print_exc()
                return

        # 常规任务处理
        for attachments in self.attachment:
            self.attachments = attachments
            try:
                self.type = self.attachments["type"]
                if (
                    self.type == "document"
                    or self.type == "ppt"
                    or self.type == "pdf"
                    or self.type == "img"
                    or self.type == ".pptx"
                ):
                    self.jobid = self.attachments["jobid"]
                    self.jtoken = self.attachments["jtoken"]
                    self.DocumentTask()
                if self.type == "video":
                    self.dtype = "Video"
                    if "audio" in self.attachments["property"]["module"]:
                        self.dtype = "Audio"
                    try:
                        self.rt = self.attachments["property"]["rt"]
                    except:
                        self.rt = "0.9"
                    try:
                        self.attDurationEnc = self.attachments["attDurationEnc"]
                    except:
                        self.attDurationEnc = None
                    try:
                        self.videoFaceCaptureEnc = self.attachments[
                            "videoFaceCaptureEnc"
                        ]
                    except:
                        self.videoFaceCaptureEnc = ""
                    self.jobid = self.attachments["jobid"]
                    self.otherInfo = self.attachments["otherInfo"].split("&")[0]
                    self.mid = self.attachments["mid"]
                    self.objectId = self.attachments["objectId"]
                    self.fid = self.defaults["fid"]
                    vdo = VideoStart(
                        self.session,
                        self.list_info,
                        self.rt,
                        self.attDurationEnc,
                        self.videoFaceCaptureEnc,
                        self.jobid,
                        self.otherInfo,
                        self.objectId,
                        self.dtype,
                        self.mid,
                        self.chapterId,
                        self.fid,
                        self.username,
                    )
                    vdo.play()
                if self.type == "workid":
                    self.worknoum = 0
                    self.otherInfo = self.attachments["otherInfo"]
                    self.workenc302 = self.attachments["enc"]
                    self.jobid = self.attachments["property"]["_jobid"]
                    self.workid = str(self.jobid).replace("work-", "")
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.success(f"ID:{self.username},准备执行测试")
                    self.WorkTask()
                if self.type == "assignment":
                    self.workenc302 = self.attachments.get("enc", "")
                    self.workid = self.attachments.get("workId", "")
                    self.answerId = self.attachments.get("answerId", "")
                    self.link = self.attachments.get("link", "")  # 新增link参数
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.success(f"ID:{self.username},准备执行作业")
                    self.AssignmentTask()
                if self.type == "hyperlink":  # 链接任务
                    self.jobid = self.attachments["jobid"]
                    self.jtoken = self.attachments["jtoken"]
                    self.LinkTask()
                if self.type == "read":
                    self.jtoken = self.attachments["jtoken"]
                    self.jobid = self.attachments["jobid"]
                    self.ReadTask()
                if self.type == "microCourse":  # 小方框 不是音频的小听力
                    self.jobid = self.attachments["property"]["jobid"]
                    self.jtoken = self.attachments["jtoken"]
                    self.microCourse_Task()
                if self.type == "vote":
                    self.jtoken = self.attachments["jtoken"]
                    self.toupiao_Task()
                if self.type == "live":
                    try:
                        self.liveSwDsEnc = self.attachments["liveSwDsEnc"]
                        self.jobid = self.attachments["jobid"]
                        self.liveId = self.attachments["property"]["liveId"]
                        self.streamName = self.attachments["property"]["streamName"]
                        self.liveDragEnc = self.attachments["liveDragEnc"]
                        self.liveSetEnc = self.attachments["liveSetEnc"]
                        self.authEnc = self.attachments["authEnc"]
                        self.vdoid = self.attachments["property"]["vdoid"]
                        self.aid = self.attachments["aid"]
                        self.LiveTake()
                        time.sleep(5)

                    except:
                        pass
            except:
                # traceback.print_exc()
                try:
                    self.Topic()
                except:
                    pass
                try:
                    jobid = self.attachments["jobid"]
                    jtoken = self.attachments["jtoken"]
                    url = f"https://mooc1.chaoxing.com/ananas/job?jobid={jobid}&knowledgeid={self.chapterId}&courseid={self.courseid}&clazzid={self.clazzid}&jtoken={jtoken}&_dc={time.time() * 1000}"
                    r = self.session.get(url).text
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.success(f"ID{self.username},文档任务点提交:{r}")
                except:
                    pass
            time.sleep(0.5)

    def Topic(self):
        mid = self.attachments["property"]["mid"]
        jobid = self.attachments["jobid"]
        r = self.session.get(
            f"https://mooc1.chaoxing.com/mooc-ans/bbscircle/chapter",
            params={
                "mtopicid": mid,
                "jobid": jobid,
                "isPortal": "false",
                "knowledgeid": self.chapterId,
                "ut": "s",
                "clazzId": self.clazzid,
                "enc": "",
                "utenc": "",
                "courseid": self.courseid,
            },
        ).text
        url_tl = parsel.Selector(r).xpath("//div[@id='topicMainDiv']/@data").get()
        res = self.session.get(url_tl).text
        text = parsel.Selector(res).xpath("/html/body/script[58]").get()
        Token = re.search(r"urlToken:\s*'([0-9a-f]+)'", text).group(1)
        pattern = r"/([a-f0-9]{32})/([a-f0-9]{32})/"
        match = re.search(pattern, url_tl)
        self.bbsid = match.group(1)  # 47f053e31ca4f25997f0d9c0f9ca1d10
        self.uuid_topic = match.group(2)  # 80778383216c4390baa14c703fff0a62
        r = self.session.get(
            f"https://groupweb.chaoxing.com/pc/invitation/getReplyList?bbsid={self.bbsid}&uuid={self.uuid_topic}&tag=&order=2&lastValue=&lastAuxValue="
        ).json()
        if r["datas"]:
            content = random.choice(r["datas"])["content"]
            logger.info(f"ID{self.username},随机复制评论内容为:{content}")
            r = self.session.post(
                f"https://groupweb.chaoxing.com/pc/invitation/{self.uuid_topic}/addReplys",
                params={
                    "courseId": self.courseid,
                    "clazzid": self.clazzid,
                    "replyId": "-1",
                    "uuid": random_uuid(),
                    "topic_content": content,
                    "anonymous": "",
                    "urlToken": Token,
                    "bbsid": self.bbsid,
                },
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0",
                    "X-Requested-With": "XMLHttpRequest",
                },
            )
            logger.success(f"ID{self.username},提交讨论:{r.text}")

    def DocumentTask(self):
        api = f"https://mooc1-api.chaoxing.com/ananas/job/document"
        params = {
            "jobid": self.jobid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "jtoken": self.jtoken,
            "checkMicroTopic": "true",
            "microTopicId": "undefined",
            "_dc": int(time.time() * 1000),
        }
        r = self.session.get(api, params=params).json()

    def WorkTask(self):
        api = f"https://mooc1-api.chaoxing.com/mooc-ans/work/phone/work"
        params = {
            "workId": self.workid,
            "courseId": self.courseid,
            "clazzId": self.clazzid,
            "knowledgeId": self.chapterId,
            "jobId": self.jobid,
            "enc": self.workenc302,
            "cpi": self.cpi,
        }
        r = self.session.get(
            api, params=params, headers=UA().APP, allow_redirects=False
        )
        if r.status_code != 200:

            api = r.headers.get("Location")
            r = self.session.get(api, headers=UA().APP).text
            html_work = StaratWorkTaks(
                self.session,
                self.courseid,
                self.clazzid,
                self.cpi,
                self.chapterId,
                self.jobid,
                self.kcname,
                self.username,
                "chapter_test"  # 明确标识为章节测验
            )
            html_work.Html_Wkrk(r)

    def AssignmentTask(self):
        """处理作业页面的答题功能，适用于平台ID 9004"""
        try:
            # 判断是否需要减少日志输出
            reduce_logs = hasattr(self.session, "cid") and self.session.cid == 9004

            # 构建作业页面URL
            api = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/dowork"
            params = {
                "courseId": self.courseid,
                "classId": self.clazzid,
                "cpi": self.cpi,
                "workId": self.workid,
                "answerId": self.answerId,
                "enc": self.workenc302,
            }

            # 仅在非9004平台ID时输出详细日志
            if not reduce_logs:
                logger.info(f"ID:{self.username},访问作业页面: {api}，参数: {params}")

            # 获取作业页面内容
            r = self.session.get(
                api, params=params, headers=UA().WEB, allow_redirects=True
            )
            if r.status_code == 200:
                # 仅在非9004平台ID时输出详细日志
                if not reduce_logs:
                    logger.success(f"ID:{self.username},成功获取作业页面")

                # 如果HomeworkAI可用，则尝试使用AI自动填充
                if HOMEWORK_AI_AVAILABLE:
                    try:
                        # 仅在非9004平台ID时输出详细日志
                        if not reduce_logs:
                            logger.info(f"ID:{self.username},尝试使用AI自动填充答案")

                        homework_ai = HomeworkAI(self.session, self.username)
                        url = (
                            f"{api}?{'&'.join([f'{k}={v}' for k, v in params.items()])}"
                        )
                        result = homework_ai.process_homework(url, self.session.cid)

                        if result:
                            # 仅在非9004平台ID时输出详细日志
                            if not reduce_logs:
                                logger.success(f"ID:{self.username},AI自动填充答案成功")
                            return
                        else:
                            # 仅在非9004平台ID时输出详细日志
                            if not reduce_logs:
                                logger.warning(
                                    f"ID:{self.username},AI自动填充答案失败，将使用常规方式处理"
                                )
                    except Exception as e:
                        # 仅在非9004平台ID时输出详细日志
                        if not reduce_logs:
                            logger.error(
                                f"ID:{self.username},使用AI自动填充答案时出错: {str(e)}"
                            )
                            logger.warning(f"ID:{self.username},将使用常规方式处理作业")

                # 使用与章节测验相同的答题处理逻辑
                html_work = StaratWorkTaks(
                    self.session,
                    self.courseid,
                    self.clazzid,
                    self.cpi,
                    self.chapterId,
                    self.jobid,
                    self.kcname,
                    self.username,
                )
                html_work.Html_Wkrk(r.text)
            else:
                # 如果直接访问失败，尝试获取重定向URL
                if "Location" in r.headers:
                    redirect_url = r.headers["Location"]
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.info(
                            f"ID:{self.username},作业页面重定向到: {redirect_url}"
                        )

                    r2 = self.session.get(redirect_url, headers=UA().WEB)
                    if r2.status_code == 200:
                        # 仅在非9004平台ID时输出详细日志
                        if not reduce_logs:
                            logger.success(
                                f"ID:{self.username},通过重定向成功获取作业页面"
                            )

                        # 如果HomeworkAI可用，则尝试使用AI自动填充
                        if HOMEWORK_AI_AVAILABLE:
                            try:
                                # 仅在非9004平台ID时输出详细日志
                                if not reduce_logs:
                                    logger.info(
                                        f"ID:{self.username},尝试使用AI自动填充答案"
                                    )

                                homework_ai = HomeworkAI(self.session, self.username)
                                result = homework_ai.process_homework(
                                    redirect_url, self.session.cid
                                )

                                if result:
                                    # 仅在非9004平台ID时输出详细日志
                                    if not reduce_logs:
                                        logger.success(
                                            f"ID:{self.username},AI自动填充答案成功"
                                        )
                                    return
                                else:
                                    # 仅在非9004平台ID时输出详细日志
                                    if not reduce_logs:
                                        logger.warning(
                                            f"ID:{self.username},AI自动填充答案失败，将使用常规方式处理"
                                        )
                            except Exception as e:
                                # 仅在非9004平台ID时输出详细日志
                                if not reduce_logs:
                                    logger.error(
                                        f"ID:{self.username},使用AI自动填充答案时出错: {str(e)}"
                                    )
                                    logger.warning(
                                        f"ID:{self.username},将使用常规方式处理作业"
                                    )

                        html_work = StaratWorkTaks(
                            self.session,
                            self.courseid,
                            self.clazzid,
                            self.cpi,
                            self.chapterId,
                            self.jobid,
                            self.kcname,
                            self.username,
                        )
                        html_work.Html_Wkrk(r2.text)
                    else:
                        # 仅在非9004平台ID时输出详细日志
                        if not reduce_logs:
                            logger.error(
                                f"ID:{self.username},通过重定向获取作业页面失败，状态码:{r2.status_code}"
                            )
                else:
                    # 仅在非9004平台ID时输出详细日志
                    if not reduce_logs:
                        logger.error(
                            f"ID:{self.username},获取作业页面失败，状态码:{r.status_code}"
                        )
        except Exception as e:
            # 仅在非9004平台ID时输出详细日志
            if not hasattr(self.session, "cid") or self.session.cid != 9004:
                logger.error(f"ID:{self.username},处理作业异常: {str(e)}")
                traceback.print_exc()

    def LinkTask(self):
        api = f"https://mooc1.chaoxing.com/ananas/job/hyperlink"
        params = {
            "jobid": self.jobid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "jtoken": self.jtoken,
            "checkMicroTopic": "true",
            "microTopicId": "undefined",
            "_dc": int(time.time() * 1000),
        }
        r = self.session.get(api, params=params).json()
        logger.success(f"ID{self.username},{r}")

    def microCourse_Task(self):
        params = {
            "jobid": self.jobid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "jtoken": self.jtoken,
        }
        """https://mooc1.chaoxing.com/ananas/job/microCourse?jobid=1591584126513824&knowledgeid=627501631&courseid=227459791&clazzid=80832027&jtoken=b11ab9769d574001ba58b5e033269feb&jsoncallback=jQuery1124011756319335885435_1699716964155&_=1699716964156"""
        api = "https://mooc1.chaoxing.com/ananas/job/microCourse"
        r = self.session.get(api, params=params).text

    def toupiao_Task(self):
        api = f"https://mooc1.chaoxing.com/ananas/job/vote"
        params = {
            "jobid": self.jobid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "jtoken": self.jtoken,
            "_dc": int(time.time() * 1000),
        }
        wed = self.session.get(api, params=params).text

    def LiveTake(self):
        """
            获取 u ：
                58ddbc050f3e1f5b887c249a761dd887
            liveid: 8000174013922523
            userid: 256382405
            clazzid: 66861257
            knowledgeid: 634733731
            courseid: 230087271
            jobid: live-8000174013922523
            ut: s
        :return:
        """
        api = f"https://mooc1.chaoxing.com/ananas/live/relation"
        r = self.session.get(
            api,
            params={
                "courseid": self.courseid,
                "knowledgeid": self.chapterId,
                "ut": "s",
                "jobid": self.jobid,
                "aid": self.aid,
            },
        )
        api = f"https://mooc1.chaoxing.com/ananas/live/liveinfo"
        params = {
            "liveid": self.liveId,
            "userid": self.Uid,
            "clazzid": self.clazzid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "jobid": self.jobid,
            "ut": "s",
        }
        title = "异常直播 - 无任务"
        try:
            r = self.session.get(api, params=params).json()
            u = r["u"]
            chartRoomId = r["temp"]["data"]["chartRoomId"]
            title = r["temp"]["data"]["title"]
            duration = r["temp"]["data"]["duration"]
            percentValue = r["temp"]["data"]["percentValue"]  # 完成百分比
            if percentValue >= 100:
                logger.success(
                    f"ID{self.username},直播任务:{title} 完成百分比:{percentValue}% 跳过"
                )
                pass
            else:
                PlayTime = 0
                StatusTime = 30
                isStart = 0
                while True:
                    if PlayTime >= int(duration) or StatusTime >= 60:
                        startTime = self.session.get(
                            f"https://zhibo.chaoxing.com/saveTimePc",
                            params={
                                "streamName": self.streamName,
                                "vdoid": self.vdoid,
                                "userId": self.Uid,
                                "isStart": isStart,
                                "t": int(time.time() * 1000),
                                "courseId": self.courseid,
                            },
                        )
                        r = self.session.get(
                            f"https://zhibo.chaoxing.com/saveTimePc?userId={self.Uid}&courseId={self.courseid}&streamName={self.streamName}&vdoid={self.vdoid}&isStart={isStart}"
                        )
                        isStart = 1
                        if PlayTime >= int(duration):
                            PlayTime = int(duration)
                        StatusTime = 0
                        api = f"https://zhibo.chaoxing.com/apis/live/put/watchMoment"
                        params = {
                            "liveId": self.liveId,
                            "streamName": self.streamName,
                            "vdoid": self.vdoid,
                            "watchMoment": PlayTime,
                            "t": int(time.time() * 1000),
                            "u": u,
                        }
                        r = self.session.get(api, params=params)
                        if PlayTime >= int(duration):
                            break
                        PlayTime += 60
                    time.sleep(1)
                    StatusTime += 1
            time.sleep(60)
            r = self.session.get(
                f"https://mooc1.chaoxing.com/ananas/live/getnewliveid?oliveid={self.liveId}&_dc={int(time.time() * 1000)}"
            ).json()
            newLiveId = r["newLiveId"]
            logger.debug(f"ID{self.username},newLiveId:{newLiveId}")
            res = self.session.get(
                f"https://zhibo.chaoxing.com/{self.jobid.replace('live-', '')}",
                params={
                    "courseId": self.courseid,
                    "clazzid": self.clazzid,
                    "knowledgeId": self.chapterId,
                    "jobId": self.jobid,
                    "userId": self.Uid,
                    "rt": "0.9",
                    "livesetenc": self.liveSetEnc,
                    "isjob": "true",
                    "watchingInCourse": "1",
                    "customPara1": f"{self.clazzid}_{self.courseid}",
                    "customPara2": self.authEnc,
                    "jobfs": "0",
                    "isNotDrag": "0",
                    "livedragenc": self.liveDragEnc,
                    "sw": "0",
                    "ds": "1",
                    "liveswdsenc": self.liveSwDsEnc,
                },
            ).status_code
            r = self.session.post(
                "https://zhibo.chaoxing.com/im/getChatStatus",
                params={"rid": chartRoomId},
            )
            logger.debug(f"ID{self.username},直播任务完成:{res} - {r.text}")
        except:
            logger.success(f"ID{self.username},直播任务:{title} 未开始")

    def ReadTask(self):
        # 判断是否需要减少日志输出
        reduce_logs = hasattr(self.session, "cid") and self.session.cid == 9004

        api = f"https://mooc1.chaoxing.com/ananas/job/readv2"
        params = {
            "jobid": self.jobid,
            "knowledgeid": self.chapterId,
            "courseid": self.courseid,
            "clazzid": self.clazzid,
            "jtoken": self.jtoken,
            "checkMicroTopic": "true",
            "microTopicId": "0",
            "_dc": int(time.time() * 1000),
        }
        r = self.session.get(api, params=params).text
        # 仅在非9004平台ID时输出详细日志
        if not reduce_logs:
            logger.success(f"ID{self.username},", r)


class AssignmentTask:
    """处理平台ID 9004的作业任务"""

    def __init__(self, session, list_info, assignment_info, username, oid=None):
        self.session = session
        self.username = username
        self.list_info = list_info
        self.courseid = list_info[0]["courseid"]
        self.clazzid = list_info[0]["clazzid"]
        self.kcname = list_info[0]["kcname"]
        self.cpi = list_info[0]["cpi"]
        self.oid = oid  # 添加oid参数

        # 解析作业信息
        self.workid = assignment_info.get("workId", "")
        self.answerId = assignment_info.get("answerId", "")
        self.enc = assignment_info.get("enc", "")
        self.knowledgeId = assignment_info.get("knowledgeId", "0")
        self.link = assignment_info.get("link", "")  # 添加link参数

        # 支持standardEnc参数
        if "standardEnc" in assignment_info:
            self.standardEnc = assignment_info.get("standardEnc", "")
            # 仅在非9004平台ID时输出详细日志
            if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                logger.info(
                    f"ID:{self.username},加载standardEnc参数: {self.standardEnc}"
                )

    def process(self):
        """处理作业任务"""
        try:
            # 检查是否有足够的参数
            if not self.workid:
                # 平台ID 9004 特殊处理
                if hasattr(self.session, "cid") and self.session.cid == 9004:
                    return False
                logger.error(f"ID:{self.username},缺少必要参数: workId为空")
                return False

            # 如果有oid，设置到session中，以便WorkTask可以更新进度
            if self.oid:
                self.session.oid = self.oid

            # 如果有link参数，直接使用作业链接
            if hasattr(self, "link") and self.link:
                # 直接使用完整的作业链接
                task_url = self.link
            else:
                # 构建作业页面URL - 优先使用task接口，这是更可靠的接口
                task_api = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task"
                task_params = {
                    "courseId": self.courseid,
                    "classId": self.clazzid,
                    "cpi": self.cpi,
                    "workId": self.workid,
                    "answerId": self.answerId,
                    "enc": self.enc,
                }

                # 如果有standardEnc参数，添加到请求参数中
                if hasattr(self, "standardEnc") and self.standardEnc:
                    task_params["standardEnc"] = self.standardEnc

                # 构建完整的URL
                task_url = f"{task_api}?{'&'.join([f'{k}={v}' for k, v in task_params.items()])}"

            # 如果HomeworkAI可用，则尝试使用AI自动填充
            if HOMEWORK_AI_AVAILABLE:
                try:
                    # 平台ID 9004 特殊处理，减少日志输出
                    if hasattr(self.session, "cid") and self.session.cid == 9004:
                        homework_ai = HomeworkAI(self.session, self.username)
                        result = homework_ai.process_homework(
                            task_url, self.session.cid
                        )
                        if result:
                            return True
                    else:
                        logger.info(f"ID:{self.username},尝试使用AI自动填充答案")
                        homework_ai = HomeworkAI(self.session, self.username)
                        result = homework_ai.process_homework(task_url)
                        if result:
                            logger.success(f"ID:{self.username},AI自动填充答案成功")
                            return True
                        else:
                            logger.warning(
                                f"ID:{self.username},AI自动填充答案失败，将尝试常规方式处理"
                            )
                except Exception as e:
                    # 平台ID 9004 特殊处理，减少日志输出
                    if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                        logger.error(
                            f"ID:{self.username},使用AI自动填充答案时出错: {str(e)}"
                        )
                        logger.warning(f"ID:{self.username},将尝试常规方式处理作业")

            # 如果AI处理失败或不可用，尝试常规处理方式
            # 获取作业页面内容
            try:
                r = self.session.get(
                    task_url, headers={"User-Agent": UA().WEB}, allow_redirects=True
                )

                if r.status_code == 200:
                    # 平台ID 9004 特殊处理，减少日志输出
                    if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                        logger.success(f"ID:{self.username},成功获取作业页面")

                    # 使用WorkTask处理作业页面
                    html_work = StaratWorkTaks(
                        self.session,
                        self.courseid,
                        self.clazzid,
                        self.cpi,
                        self.knowledgeId,
                        self.answerId,
                        self.kcname,
                        self.username,
                        "assignment"  # 明确标识为作业
                    )
                    html_work.Html_Wkrk(r.text)
                    return True
                elif "Location" in r.headers:
                    # 处理重定向
                    redirect_url = r.headers["Location"]

                    # 平台ID 9004 特殊处理，减少日志输出
                    if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                        logger.info(
                            f"ID:{self.username},作业页面重定向到: {redirect_url}"
                        )

                    # 尝试跟随重定向
                    r2 = self.session.get(
                        redirect_url, headers={"User-Agent": UA().WEB}
                    )
                    if r2.status_code == 200:
                        # 平台ID 9004 特殊处理，减少日志输出
                        if not (
                            hasattr(self.session, "cid") and self.session.cid == 9004
                        ):
                            logger.success(
                                f"ID:{self.username},通过重定向成功获取作业页面"
                            )

                        # 使用WorkTask处理作业页面
                        html_work = StaratWorkTaks(
                            self.session,
                            self.courseid,
                            self.clazzid,
                            self.cpi,
                            self.knowledgeId,
                            self.answerId,
                            self.kcname,
                            self.username,
                            "assignment"  # 明确标识为作业
                        )
                        html_work.Html_Wkrk(r2.text)
                        return True
                    else:
                        # 平台ID 9004 特殊处理，减少日志输出
                        if not (
                            hasattr(self.session, "cid") and self.session.cid == 9004
                        ):
                            logger.error(
                                f"ID:{self.username},通过重定向获取作业页面失败，状态码: {r2.status_code}"
                            )
                        return False
                else:
                    # 平台ID 9004 特殊处理，减少日志输出
                    if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                        logger.error(
                            f"ID:{self.username},获取作业页面失败，状态码: {r.status_code}"
                        )
                    return False
            except Exception as e:
                # 平台ID 9004 特殊处理，减少日志输出
                if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                    logger.error(f"ID:{self.username},访问作业页面异常: {str(e)}")
                return False

        except Exception as e:
            # 平台ID 9004 特殊处理，减少日志输出
            if not (hasattr(self.session, "cid") and self.session.cid == 9004):
                logger.error(f"ID:{self.username},处理作业异常: {str(e)}")
                traceback.print_exc()
            return False
