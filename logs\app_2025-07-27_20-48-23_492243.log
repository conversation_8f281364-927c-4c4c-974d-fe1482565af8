2025-07-27 20:48:23.493 | INFO     | __main__:<module>:1894 - 学习通自动化系统启动...
2025-07-27 20:48:23.494 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-27 20:48:23.494 | SUCCESS  | __main__:<module>:1904 - 数据库连接池初始化成功
2025-07-27 20:48:23.516 | SUCCESS  | __main__:<module>:1925 - 数据状态重置完成
2025-07-27 20:48:23.517 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 0/100
2025-07-27 20:48:23.517 | INFO     | __main__:<module>:1941 - 开始处理订单...
2025-07-27 20:48:23.518 | INFO     | __main__:order_get:1697 - 订单处理线程启动，最大线程数: 100
2025-07-27 20:48:23.521 | INFO     | __main__:order_get:1722 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-27 20:48:23.521 | INFO     | __main__:Run:1781 - 开始处理订单: OID=297357, 用户=***********, 课程ID=*********
2025-07-27 20:48:23.521 | INFO     | __main__:register_thread:67 - 注册线程: OID=297357, 当前活跃线程数: 1
2025-07-27 20:48:24.066 | SUCCESS  | __main__:Run:1825 - ID:***********,登录成功
2025-07-27 20:48:24.068 | INFO     | __main__:kclist:254 - ID:***********,正在获取课程列表...
2025-07-27 20:48:24.659 | INFO     | __main__:kclist:256 - ID:***********,课程列表API返回: {'result': 1, 'msg': '获取成功', 'channelList': [{'cfid': -1, 'norder': 72, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108147120, 'content': {'studentcount': 35, 'chatid': '262452157677570', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王欣欣', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=247101786&personId=282371395&classId=108147120&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '建筑设备与识图', 'id': 247101786}]}, 'roletype': 3, 'id': 108147120, 'state': 0, 'cpi': 282371395, 'bbsid': '233d0c9384c7fd49ef30471e70b6f18d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 70, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 103243606, 'content': {'studentcount': 37, 'chatid': '257804644450310', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '9136', 'coursestate': 0, 'teacherfactor': '辛星、孙万香', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245076370&personId=306706095&classId=103243606&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '现代建筑施工项目管理', 'id': 245076370}]}, 'roletype': 3, 'id': 103243606, 'state': 0, 'cpi': 306706095, 'bbsid': '82e9a0452a5fb0153a08742d38d2e5d3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 62, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93174104, 'content': {'studentcount': 36, 'chatid': '240755038814211', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=240249074&personId=282371395&classId=93174104&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '装配式构造与识图', 'id': 240249074}]}, 'roletype': 3, 'id': 93174104, 'state': 0, 'cpi': 282371395, 'bbsid': '9c443055acdff2d922c91a98f0d01497', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 61, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93031937, 'content': {'studentcount': 37, 'chatid': '240686109622276', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '乔颖秀', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241048997&personId=282371395&classId=93031937&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '建筑装饰施工技术', 'id': 241048997}]}, 'roletype': 3, 'id': 93031937, 'state': 0, 'cpi': 282371395, 'bbsid': '8bc656572d3aeb51ff07fd8df2167bde', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 58, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': ********, 'content': {'studentcount': 36, 'chatid': '235813675794433', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '6,9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=*********&personId=282371395&classId=********&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/b34d7695e8fd9ce297ef09b2bcb31608.jpg', 'name': '工程测量', 'id': *********}]}, 'roletype': 3, 'id': ********, 'state': 0, 'cpi': 282371395, 'bbsid': 'bee20260b71f18a382f79be093c75781', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 50, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 84338229, 'content': {'studentcount': 36, 'chatid': '228028031500290', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵翠', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237732213&personId=282371395&classId=84338229&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '监理概论', 'id': 237732213}]}, 'roletype': 3, 'id': 84338229, 'state': 0, 'cpi': 282371395, 'bbsid': 'c7b537a1f1f64b3e1f029c0b1e6f3ac9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 41, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81695272, 'content': {'studentcount': 39, 'chatid': '225435820556293', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=222501937&personId=282371395&classId=81695272&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/02dd581c5cfd7d4840aa0924a5741b87.png', 'name': '砌体工程施工', 'id': 222501937}]}, 'roletype': 3, 'id': 81695272, 'state': 0, 'cpi': 282371395, 'bbsid': '50a0052e1faaf398eab9eed65fdcbbe6', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 34, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77731070, 'content': {'studentcount': 37, 'chatid': '214216739979265', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=206940210&personId=282371395&classId=77731070&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/5fcb560ba7bde03929ccae19113a260a.png', 'name': '房屋构造', 'id': 206940210}]}, 'roletype': 3, 'id': 77731070, 'state': 0, 'cpi': 282371395, 'bbsid': '727dcfd2ef24a37e905835bc220b2e79', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 29, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 73006297, 'content': {'studentcount': 36, 'chatid': '207071973801986', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232937843&personId=282371395&classId=73006297&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/445cfff6239083f79374551111fb385b.png', 'name': '二十四式简化太极拳', 'id': 232937843}]}, 'roletype': 3, 'id': 73006297, 'state': 0, 'cpi': 282371395, 'bbsid': 'bd43bf14e2bae80865bad5e04a164058', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 24, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63861990, 'content': {'studentcount': 56, 'chatid': '193216339050497', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102、装配22101', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王波', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228707968&personId=282371395&classId=63861990&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '应用数学A（一）', 'id': 228707968}]}, 'roletype': 3, 'id': 63861990, 'state': 0, 'cpi': 282371395, 'bbsid': 'd879253745a36fc8ffa778689174eb5f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 13, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64051718, 'content': {'studentcount': 38, 'chatid': '193380112990210', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张睿', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228856150&personId=282371395&classId=64051718&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/ee1c91e5d692ad719f7edd5c90a3625e.jpg', 'name': '2022级大学英语', 'id': 228856150}]}, 'roletype': 3, 'id': 64051718, 'state': 0, 'cpi': 282371395, 'bbsid': '1df2bd032967a8bdb8a0185c961aceff', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 12, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63990559, 'content': {'studentcount': 37, 'chatid': '193312997834753', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黎佳媚', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228506578&personId=239575512&classId=63990559&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生心理健康', 'id': 228506578}]}, 'roletype': 3, 'id': 63990559, 'state': 0, 'cpi': 239575512, 'bbsid': '63e85270f677fb0d9ca5bc5a66abcf82', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 11, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63680895, 'content': {'studentcount': 37, 'chatid': '193107346915329', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=219619084&personId=239575512&classId=63680895&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑力学与结构', 'id': 219619084}]}, 'roletype': 3, 'id': 63680895, 'state': 0, 'cpi': 239575512, 'bbsid': '83e4bf9de026eecc5d2c0344a0d72062', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 10, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63759655, 'content': {'studentcount': 36, 'chatid': '193145009668098', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '崔炳文', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228721228&personId=239575512&classId=63759655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生职业发展与就业指导', 'id': 228721228}]}, 'roletype': 3, 'id': 63759655, 'state': 0, 'cpi': 239575512, 'bbsid': '4247ba6c3c91d5fe2b0847f4fb2dab18', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 3, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63765619, 'content': {'studentcount': 36, 'chatid': '193151980601345', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228724038&personId=282371395&classId=63765619&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/4d22019e5c81452b5fc4082ac48a0fc0.png', 'name': '体育与健康课-西职院-建筑与轨道交通学院2022级', 'id': 228724038}]}, 'roletype': 3, 'id': 63765619, 'state': 0, 'cpi': 282371395, 'bbsid': 'bfb1bf61cb93753dc55127646480fb23', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 2, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64009643, 'content': {'studentcount': 35, 'chatid': '193329581064194', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王觅', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228836418&personId=282371395&classId=64009643&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑工程识图（1+X）', 'id': 228836418}]}, 'roletype': 3, 'id': 64009643, 'state': 0, 'cpi': 282371395, 'bbsid': 'c974502359bdde67e6a49c879e1c5a4c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 71, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108143976, 'content': {'studentcount': 38, 'chatid': '262448649142274', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月  王成平 吉海军 孟琳 牛欣欣       西安职业技术学院', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245183825&personId=282371395&classId=108143976&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/00ceb684f7b286cb38e7a610330ee3bb.png', 'name': '建筑类专业创新创业基础', 'id': 245183825}]}, 'roletype': 3, 'id': 108143976, 'state': 1, 'cpi': 282371395, 'bbsid': 'a61bd4f169036c406137776a7e69edd4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 67, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 99702566, 'content': {'studentcount': 31, 'chatid': '249557002813441', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙万香、张阳、乔颖秀，李頔、刘颖、伦琳琳、吕萍', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=242066105&personId=282371395&classId=99702566&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/e15f9d0fa84e0dc88cf6bee453692061.jpg', 'name': '居住空间设计', 'id': 242066105}]}, 'roletype': 3, 'id': 99702566, 'state': 1, 'cpi': 282371395, 'bbsid': 'f7f88c1f5d8e2510715d9d40c0f52c11', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 66, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 98878754, 'content': {'studentcount': 36, 'chatid': '247959327408129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241528817&personId=282371395&classId=98878754&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 241528817}]}, 'roletype': 3, 'id': 98878754, 'state': 1, 'cpi': 282371395, 'bbsid': 'bc75f264d544b6db51bc97e06a2dbffb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 65, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 96477120, 'content': {'studentcount': 66, 'chatid': '243660016910338', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22101班+22102班（张莹霞）', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '高静娜王世伟王颖易云王虹田雨李芮王怡玮张莹霞何慧琦刘景华马岩李晓航范建梅李殷红郭炯张哲王帅军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241400281&personId=282371395&classId=96477120&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/411a62e07ee35beb471ff8591e7c0583.png', 'name': '习近平新时代中国特色社会主义思想概论', 'id': 241400281}]}, 'roletype': 3, 'id': 96477120, 'state': 1, 'cpi': 282371395, 'bbsid': 'a31e26a3f7dcfe346e427926a0c12843', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 64, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 94747290, 'content': {'studentcount': 36, 'chatid': '241659454488581', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241352309&personId=282371395&classId=94747290&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 241352309}]}, 'roletype': 3, 'id': 94747290, 'state': 1, 'cpi': 282371395, 'bbsid': '8615728073e0266575a4f2634f73f4b4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 63, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93458508, 'content': {'studentcount': 37, 'chatid': '240865358446597', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王成平', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241226635&personId=282371395&classId=93458508&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0078f13df62d385adbf64bd357801250.jpg', 'name': '装配式建筑构件制作与安装（1+X）', 'id': 241226635}]}, 'roletype': 3, 'id': 93458508, 'state': 1, 'cpi': 282371395, 'bbsid': '54f658b680b207e33f03dc30f26eaaf9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 57, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 90776227, 'content': {'studentcount': 36, 'chatid': '235091979730946', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=239229163&personId=282371395&classId=90776227&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 239229163}]}, 'roletype': 3, 'id': 90776227, 'state': 1, 'cpi': 282371395, 'bbsid': '3fa527de268d07e7f56fd000639a6280', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 56, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86627150, 'content': {'studentcount': 39, 'chatid': '230251015766019', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257713&personId=282371395&classId=86627150&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 236257713}]}, 'roletype': 3, 'id': 86627150, 'state': 1, 'cpi': 282371395, 'bbsid': 'c9e6fc8ae6f2a130c48c7e7781f42187', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 55, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86450001, 'content': {'studentcount': 36, 'chatid': '230515973095428', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257673&personId=282371395&classId=86450001&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 236257673}]}, 'roletype': 3, 'id': 86450001, 'state': 1, 'cpi': 282371395, 'bbsid': 'ccf2d9d3dd2cdde64beec68109dea4b0', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 53, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568471, 'content': {'studentcount': 184, 'chatid': '226902282403844', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黄玉顺', 'isCourseSquare': 0, 'schools': '山东大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417781&personId=282371395&classId=83568471&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cac10875886f12e31fa939a4b330acc2.jpg', 'name': '儒学与生活', 'id': 237417781}]}, 'roletype': 3, 'id': 83568471, 'state': 1, 'cpi': 282371395, 'bbsid': 'd5b6945eac682309e1c07aaf36380f67', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 52, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568466, 'content': {'studentcount': 84, 'chatid': '226902281355269', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙劲松 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417778&personId=282371395&classId=83568466&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cdcd8e1a0722b927b2bd2370ab0ca37f.jpg', 'name': '《周易》的奥秘', 'id': 237417778}]}, 'roletype': 3, 'id': 83568466, 'state': 1, 'cpi': 282371395, 'bbsid': '3905ad7fe8b5446aa42aba53526e017c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 51, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568419, 'content': {'studentcount': 186, 'chatid': '226902271918084', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李义平', 'isCourseSquare': 0, 'schools': '中国人民大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417727&personId=282371395&classId=83568419&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star/270_169c/1395222258227jqpbs.png', 'name': '经济学百年', 'id': 237417727}]}, 'roletype': 3, 'id': 83568419, 'state': 1, 'cpi': 282371395, 'bbsid': '8c3ac1bcb7e980cd757784e60d19c2f1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 48, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83894032, 'content': {'studentcount': 34, 'chatid': '227791773696001', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '袁敏、李缪美、冯三琴、陈萌、赵亚楠', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236205491&personId=282371395&classId=83894032&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/2b4f778a10dc049954c0703905744ed6.jpg', 'name': '中华优秀传统文化', 'id': 236205491}]}, 'roletype': 3, 'id': 83894032, 'state': 1, 'cpi': 282371395, 'bbsid': '2ef8c7abe16df4475e4c3608820689e1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 47, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83565558, 'content': {'studentcount': 3241, 'chatid': '226899079004165', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '刘剑 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363016&personId=282371395&classId=83565558&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/033b195bb4d859c34f837436a2e2b047.png', 'name': '职业生涯提升', 'id': 237363016}]}, 'roletype': 3, 'id': 83565558, 'state': 1, 'cpi': 282371395, 'bbsid': 'da7fb09f3a83f26e851457f5b6c7139c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 46, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83563993, 'content': {'studentcount': 3241, 'chatid': '226898253774851', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363015&personId=282371395&classId=83563993&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 237363015}]}, 'roletype': 3, 'id': 83563993, 'state': 1, 'cpi': 282371395, 'bbsid': '32e5fc34f009a71da5533841a69b8d4f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 44, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 82934974, 'content': {'studentcount': 37, 'chatid': '226347724111878', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236255632&personId=282371395&classId=82934974&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 236255632}]}, 'roletype': 3, 'id': 82934974, 'state': 1, 'cpi': 282371395, 'bbsid': '0930cc6c545794f09023829842e80bbb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 42, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81792501, 'content': {'studentcount': 40, 'chatid': '225462012936193', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225074231&personId=282371395&classId=81792501&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ccf2e893a0fec74beb2c807bed1380fe.png', 'name': '钢筋混凝土工程施工', 'id': 225074231}]}, 'roletype': 3, 'id': 81792501, 'state': 1, 'cpi': 282371395, 'bbsid': 'aab65d5d802d23a526d71e6cf24fa6ec', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 40, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 79340160, 'content': {'studentcount': 306, 'chatid': '218929127555073', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2023春校内选课4班', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '关蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232872307&personId=306706095&classId=79340160&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/45d95091261bf5413d34711b4812f878.png', 'name': '城市轨道交通行车组织', 'id': 232872307}]}, 'roletype': 3, 'id': 79340160, 'state': 1, 'cpi': 306706095, 'bbsid': 'ada486380464473062934e91bb473e95', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 35, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 78162387, 'content': {'studentcount': 101, 'chatid': '215496757673985', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '1+X培训', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=78162387&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 78162387, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 33, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77274590, 'content': {'studentcount': 36, 'chatid': '212705581531137', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=77274590&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 77274590, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 31, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74231723, 'content': {'studentcount': 37, 'chatid': '208325644976129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233334480&personId=282371395&classId=74231723&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 233334480}]}, 'roletype': 3, 'id': 74231723, 'state': 1, 'cpi': 282371395, 'bbsid': 'ac44ce09c57beb7098f3962568c9b5da', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 30, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74122655, 'content': {'studentcount': 38, 'chatid': '208076318769153', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233076005&personId=282371395&classId=74122655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 233076005}]}, 'roletype': 3, 'id': 74122655, 'state': 1, 'cpi': 282371395, 'bbsid': 'e18b21fa283bde3c284082489b2e2d9d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 28, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 72909517, 'content': {'studentcount': 42, 'chatid': '207009144176641', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232915084&personId=282371395&classId=72909517&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/8da13f152a75aa27fd646b620d4a785c.jpg', 'name': 'BIM技术Revit软件建模', 'id': 232915084}]}, 'roletype': 3, 'id': 72909517, 'state': 1, 'cpi': 282371395, 'bbsid': '37149f94ac3f555df5c8e9f9e7bba0c3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 20, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 69595761, 'content': {'studentcount': 37, 'chatid': '200442127646722', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225425874&personId=239575512&classId=69595761&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 225425874}]}, 'roletype': 3, 'id': 69595761, 'state': 1, 'cpi': 239575512, 'bbsid': 'b83a68cde1f0614c47b8e2cd2d0b6ccf', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 19, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66385034, 'content': {'studentcount': 103, 'chatid': '196295283245059', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '杨树山', 'isCourseSquare': 0, 'schools': '天津理工大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229863793&personId=282371395&classId=66385034&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/56d95359e4b0dfadae7a3f77.jpg', 'name': '漫画艺术欣赏与创作', 'id': 229863793}]}, 'roletype': 3, 'id': 66385034, 'state': 1, 'cpi': 282371395, 'bbsid': 'cee2bff5ffef3ec232d3843cdc413c6f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 18, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66384671, 'content': {'studentcount': 50, 'chatid': '196295121764358', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '段鑫星', 'isCourseSquare': 0, 'schools': '中国矿业大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229864935&personId=282371395&classId=66384671&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6a97d52937f928f858572fa3d0cc9e9a.jpg', 'name': '恋爱心理学', 'id': 229864935}]}, 'roletype': 3, 'id': 66384671, 'state': 1, 'cpi': 282371395, 'bbsid': 'd269da8fd0755fc780863bd28fd29ff4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 17, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115796, 'content': {'studentcount': 3586, 'chatid': '195917631258628', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张国清', 'isCourseSquare': 0, 'schools': '同济大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745592&personId=282371395&classId=66115796&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/f01bc30632e023f83b3e8879cdeea2c7.jpg', 'name': '军事理论', 'id': 229745592}]}, 'roletype': 3, 'id': 66115796, 'state': 1, 'cpi': 282371395, 'bbsid': '2d62f1844657fabcd6f9a66200efc9ce', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 16, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115802, 'content': {'studentcount': 3268, 'chatid': '195917634404353', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745593&personId=282371395&classId=66115802&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 229745593}]}, 'roletype': 3, 'id': 66115802, 'state': 1, 'cpi': 282371395, 'bbsid': '3294c8c4ccdf002e427fba19c732b9c8', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 15, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115811, 'content': {'studentcount': 3268, 'chatid': '195917639647234', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '齐晖 等', 'isCourseSquare': 0, 'schools': '中原工学院', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745598&personId=282371395&classId=66115811&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/560a0b0ee4b040cfea1a0f68.jpg', 'name': '大学计算机基础', 'id': 229745598}]}, 'roletype': 3, 'id': 66115811, 'state': 1, 'cpi': 282371395, 'bbsid': '015404e051dbfe6a573acaa45e6b29dc', 'isSquare': 0}, 'topsign': 0}], 'mcode': '-1', 'createcourse': 1, 'teacherEndCourse': 0, 'showEndCourse': 1, 'hasMore': False, 'stuEndCourse': 1}
2025-07-27 20:48:24.674 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 247101786, 目标课程ID: *********
2025-07-27 20:48:24.674 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245076370, 目标课程ID: *********
2025-07-27 20:48:24.674 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 240249074, 目标课程ID: *********
2025-07-27 20:48:24.675 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241048997, 目标课程ID: *********
2025-07-27 20:48:24.675 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: *********, 目标课程ID: *********
2025-07-27 20:48:24.675 | SUCCESS  | __main__:kclist:298 - ID:***********,成功匹配课程: {'kcname': '工程测量', 'courseid': '*********', 'clazzid': ********, 'cpi': 282371395}
2025-07-27 20:48:24.676 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237732213, 目标课程ID: *********
2025-07-27 20:48:24.676 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 222501937, 目标课程ID: *********
2025-07-27 20:48:24.677 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 206940210, 目标课程ID: *********
2025-07-27 20:48:24.677 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232937843, 目标课程ID: *********
2025-07-27 20:48:24.677 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228707968, 目标课程ID: *********
2025-07-27 20:48:24.677 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228856150, 目标课程ID: *********
2025-07-27 20:48:24.678 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228506578, 目标课程ID: *********
2025-07-27 20:48:24.678 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 219619084, 目标课程ID: *********
2025-07-27 20:48:24.678 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228721228, 目标课程ID: *********
2025-07-27 20:48:24.679 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228724038, 目标课程ID: *********
2025-07-27 20:48:24.679 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228836418, 目标课程ID: *********
2025-07-27 20:48:24.679 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245183825, 目标课程ID: *********
2025-07-27 20:48:24.680 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 242066105, 目标课程ID: *********
2025-07-27 20:48:24.680 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241528817, 目标课程ID: *********
2025-07-27 20:48:24.680 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241400281, 目标课程ID: *********
2025-07-27 20:48:24.681 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241352309, 目标课程ID: *********
2025-07-27 20:48:24.681 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241226635, 目标课程ID: *********
2025-07-27 20:48:24.681 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 239229163, 目标课程ID: *********
2025-07-27 20:48:24.682 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257713, 目标课程ID: *********
2025-07-27 20:48:24.682 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257673, 目标课程ID: *********
2025-07-27 20:48:24.682 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417781, 目标课程ID: *********
2025-07-27 20:48:24.682 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417778, 目标课程ID: *********
2025-07-27 20:48:24.683 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417727, 目标课程ID: *********
2025-07-27 20:48:24.683 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236205491, 目标课程ID: *********
2025-07-27 20:48:24.683 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363016, 目标课程ID: *********
2025-07-27 20:48:24.684 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363015, 目标课程ID: *********
2025-07-27 20:48:24.684 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236255632, 目标课程ID: *********
2025-07-27 20:48:24.684 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225074231, 目标课程ID: *********
2025-07-27 20:48:24.684 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232872307, 目标课程ID: *********
2025-07-27 20:48:24.685 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: *********
2025-07-27 20:48:24.685 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: *********
2025-07-27 20:48:24.685 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233334480, 目标课程ID: *********
2025-07-27 20:48:24.685 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233076005, 目标课程ID: *********
2025-07-27 20:48:24.686 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232915084, 目标课程ID: *********
2025-07-27 20:48:24.687 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225425874, 目标课程ID: *********
2025-07-27 20:48:24.687 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229863793, 目标课程ID: *********
2025-07-27 20:48:24.687 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229864935, 目标课程ID: *********
2025-07-27 20:48:24.688 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745592, 目标课程ID: *********
2025-07-27 20:48:24.688 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745593, 目标课程ID: *********
2025-07-27 20:48:24.688 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745598, 目标课程ID: *********
2025-07-27 20:48:24.689 | SUCCESS  | __main__:Run:1834 - ID:***********,课程信息匹配成功
2025-07-27 20:48:25.458 | SUCCESS  | __main__:studentcourse:757 - ID:***********,课件获取完成，总共获取到34个未完成章节
2025-07-27 20:48:26.156 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 20:48:27.653 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:2%,详情:课程任务:37/109 | 章节测验: 15/49; | 实时执行：3.6 单元测验 (1/34) | 更新:2025-07-27 20:48:25
2025-07-27 20:48:29.606 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 20:48:31.019 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 20:48:31.019 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 20:48:31.025 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到28个题目
2025-07-27 20:48:31.025 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/28 个题目
2025-07-27 20:48:31.030 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:48:31.031 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:48:41.201 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:48:41.201 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:48:41.506 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:48:41.507 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:48:41.507 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:48:41.508 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:48:41.702 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:48:41.702 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:48:43.217 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 确定点的高程
2025-07-27 20:48:43.219 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A确定点的平面位置', 'B': 'B确定点的高程', 'C': 'C确定水平距离', 'D': 'D确定高差'}
2025-07-27 20:48:43.220 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 确定点的高程
2025-07-27 20:48:43.220 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:48:43.221 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:48:43.221 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 1/28 个题目
2025-07-27 20:48:43.222 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/28 个题目
2025-07-27 20:48:43.224 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:48:43.224 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:48:53.397 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:48:53.397 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:48:53.681 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:48:53.681 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:48:53.682 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:48:53.682 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:48:53.846 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:48:53.847 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:48:55.395 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 点的坐标
2025-07-27 20:48:55.397 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A点的坐标', 'B': 'B水平角', 'C': 'C垂直角', 'D': 'D视距'}
2025-07-27 20:48:55.398 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 点的坐标
2025-07-27 20:48:55.398 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 20:48:55.398 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: A
2025-07-27 20:48:55.399 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 2/28 个题目
2025-07-27 20:48:55.399 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/28 个题目
2025-07-27 20:48:55.401 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:48:55.402 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪基座上有三个脚螺旋,其主要作用是( )。...
2025-07-27 20:49:01.300 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 精确整平仪器...
2025-07-27 20:49:01.302 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A连接脚架', 'B': 'B整平仪器', 'C': 'C升降脚架', 'D': 'D调节对中'}
2025-07-27 20:49:01.302 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 精确整平仪器
2025-07-27 20:49:01.302 | INFO     | API.WorkTask:Xuan:1159 - ID:***********,尝试使用相似度匹配
2025-07-27 20:49:01.304 | INFO     | API.WorkTask:Xuan:1179 - ID:***********,尝试使用关键词匹配，答案关键词: {'精确整平仪器'}
2025-07-27 20:49:01.305 | INFO     | API.WorkTask:Xuan:1228 - ID:***********,尝试使用短语匹配，答案短语: ['精确整平仪器']
2025-07-27 20:49:01.306 | ERROR    | API.WorkTask:Xuan:1336 - ID:***********,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 20:49:01.306 | WARNING  | API.WorkTask:Xuan:1340 - ID:***********,答案无法与任何选项匹配
2025-07-27 20:49:01.306 | WARNING  | API.WorkTask:Html_Wkrk:551 - ID:***********,主题库答案处理失败，尝试备用题库
2025-07-27 20:49:01.306 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:49:01.307 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪基座上有三个脚螺旋,其主要作用是( )。...
2025-07-27 20:49:01.569 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:49:01.570 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:49:16.840 | WARNING  | API.WorkTask:get_ai_answer_for_choice:2021 - ID:***********,AI接口返回错误: API请求失败: HTTP码=0
2025-07-27 20:49:16.841 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 3/28 个题目
2025-07-27 20:49:16.841 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/28 个题目
2025-07-27 20:49:16.843 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:49:16.843 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:49:23.518 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:49:26.991 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:49:26.991 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:49:27.231 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:49:27.231 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:49:27.231 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:49:27.232 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:49:27.429 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:49:27.430 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:49:28.787 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 调节脚螺旋
2025-07-27 20:49:28.789 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A升降脚架', 'B': 'B调节脚螺旋', 'C': 'C调整脚架位置', 'D': 'D平移仪器'}
2025-07-27 20:49:28.789 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 调节脚螺旋
2025-07-27 20:49:28.790 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:49:28.790 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:49:28.791 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 4/28 个题目
2025-07-27 20:49:28.791 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/28 个题目
2025-07-27 20:49:28.793 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:49:28.793 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:49:38.967 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:49:38.967 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:49:39.193 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:49:39.194 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:49:39.194 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:49:39.195 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:49:39.384 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:49:39.385 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:49:40.862 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 望远镜物镜光心与十字丝中心的连线
2025-07-27 20:49:40.864 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A望远镜物镜光心与目镜光心的连线', 'B': 'B望远镜物镜光心与十字丝中心的连线', 'C': 'C望远镜目镜光心与十字丝中心的连线', 'D': 'D通过水准管内壁圆弧中点的切线'}
2025-07-27 20:49:40.865 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 望远镜物镜光心与十字丝中心的连线
2025-07-27 20:49:40.865 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:49:40.866 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:49:40.867 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 5/28 个题目
2025-07-27 20:49:40.867 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 6/28 个题目
2025-07-27 20:49:40.869 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:49:40.870 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:49:51.045 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:49:51.045 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:49:51.290 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:49:51.290 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:49:51.290 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:49:51.291 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:49:51.487 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:49:51.488 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:49:54.034 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 平移脚架
2025-07-27 20:49:54.036 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A伸缩脚架', 'B': 'B平移脚架', 'C': 'C调节脚螺旋', 'D': 'D平移仪器'}
2025-07-27 20:49:54.036 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 平移脚架
2025-07-27 20:49:54.037 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:49:54.037 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:49:54.038 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 6/28 个题目
2025-07-27 20:49:54.038 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 7/28 个题目
2025-07-27 20:49:54.040 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:49:54.041 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标读数,其原因在于( )。...
2025-07-27 20:50:04.195 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:50:04.195 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标读数,其原因在于( )。...
2025-07-27 20:50:04.414 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:50:04.415 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:50:04.415 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:50:04.416 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标...
2025-07-27 20:50:04.602 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:50:04.603 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:50:06.018 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 右目标读数大,左目标读数小
2025-07-27 20:50:06.019 | INFO     | API.WorkTask:get_ai_answer_for_choice:1907 - ID:***********,检测到','分隔的多选题答案，转换为###分隔
2025-07-27 20:50:06.019 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1969 - ID:***********,多选题匹配成功: A
2025-07-27 20:50:06.020 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 7/28 个题目
2025-07-27 20:50:06.020 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 8/28 个题目
2025-07-27 20:50:06.022 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:50:06.022 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°28′13″,下半测回角值为102°28′...
2025-07-27 20:50:16.202 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:50:16.202 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°28′13″,下半测回角值为102°28′...
2025-07-27 20:50:16.441 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:50:16.441 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:50:16.441 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:50:16.442 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°2...
2025-07-27 20:50:16.640 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:50:16.641 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:50:17.949 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 102°28′17″
2025-07-27 20:50:17.951 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A102°28′07″', 'B': 'B102°28′17″', 'C': 'C102°28′16″', 'D': 'D102°28′33″'}
2025-07-27 20:50:17.952 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 102°28′17″
2025-07-27 20:50:17.952 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:50:17.952 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:50:17.953 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 8/28 个题目
2025-07-27 20:50:17.953 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 9/28 个题目
2025-07-27 20:50:17.955 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:50:17.955 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是为了消除( )误差对测角的影响。...
2025-07-27 20:50:23.519 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:50:28.115 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:50:28.115 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是为了消除( )误差对测角的影响。...
2025-07-27 20:50:28.387 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:50:28.387 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:50:28.388 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:50:28.388 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是...
2025-07-27 20:50:28.587 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:50:28.588 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:50:30.059 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 照准
2025-07-27 20:50:30.061 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A对中', 'B': 'B照准', 'C': 'C目标偏离中心', 'D': 'D整平'}
2025-07-27 20:50:30.061 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 照准
2025-07-27 20:50:30.062 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:50:30.062 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:50:30.062 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 9/28 个题目
2025-07-27 20:50:30.062 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 10/28 个题目
2025-07-27 20:50:30.063 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:50:30.064 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:50:40.112 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:50:40.112 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:50:40.349 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:50:40.350 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:50:40.350 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:50:40.350 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:50:40.531 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:50:40.532 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:50:41.937 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 称为仰角,角值为正
2025-07-27 20:50:41.938 | INFO     | API.WorkTask:get_ai_answer_for_choice:1907 - ID:***********,检测到','分隔的多选题答案，转换为###分隔
2025-07-27 20:50:41.938 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1969 - ID:***********,多选题匹配成功: A
2025-07-27 20:50:41.939 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 10/28 个题目
2025-07-27 20:50:41.939 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 11/28 个题目
2025-07-27 20:50:41.941 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:50:41.941 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90°,望远镜物镜端抬高时读数减少,其盘左的...
2025-07-27 20:50:52.064 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:50:52.064 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90°,望远镜物镜端抬高时读数减少,其盘左的...
2025-07-27 20:50:52.300 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:50:52.300 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:50:52.301 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:50:52.301 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90...
2025-07-27 20:50:52.502 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:50:52.502 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:50:55.009 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: L-90°
2025-07-27 20:50:55.011 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A90°-L', 'B': 'BL-90°', 'C': 'C180°-L', 'D': 'DL-180°'}
2025-07-27 20:50:55.011 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: L-90°
2025-07-27 20:50:55.012 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:50:55.012 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:50:55.013 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 11/28 个题目
2025-07-27 20:50:55.013 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 12/28 个题目
2025-07-27 20:50:55.015 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:50:55.015 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 20:51:05.068 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:51:05.068 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 20:51:05.278 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:51:05.278 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:51:05.279 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:51:05.279 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101...
2025-07-27 20:51:05.465 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:51:05.466 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:51:07.977 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: -12″
2025-07-27 20:51:07.979 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A+24″', 'B': 'B-12″', 'C': 'C-24″', 'D': 'D+12″'}
2025-07-27 20:51:07.979 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: -12″
2025-07-27 20:51:07.980 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:51:07.980 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:51:07.980 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 12/28 个题目
2025-07-27 20:51:07.981 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 13/28 个题目
2025-07-27 20:51:07.982 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:51:07.982 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 20:51:18.163 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:51:18.163 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 20:51:18.352 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:51:18.353 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:51:18.353 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:51:18.354 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°4...
2025-07-27 20:51:18.519 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:51:18.519 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:51:19.893 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: +06″
2025-07-27 20:51:19.894 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A-06″', 'B': 'B+06″', 'C': 'C-12″', 'D': 'D+12″'}
2025-07-27 20:51:19.895 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: +06″
2025-07-27 20:51:19.895 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:51:19.895 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:51:19.896 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 13/28 个题目
2025-07-27 20:51:19.896 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 14/28 个题目
2025-07-27 20:51:19.897 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:51:19.898 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:51:23.520 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:51:30.045 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:51:30.045 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:51:30.280 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:51:30.281 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:51:30.281 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:51:30.282 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:51:30.456 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:51:30.457 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:51:31.771 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 水平角测量
2025-07-27 20:51:31.773 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A空间角测量', 'B': 'B水平角测量', 'C': 'C垂直角测量', 'D': 'D方位角测量', 'E': 'E倾斜角测量'}
2025-07-27 20:51:31.774 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 水平角测量
2025-07-27 20:51:31.774 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:51:31.774 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:51:31.774 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 14/28 个题目
2025-07-27 20:51:31.776 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 15/28 个题目
2025-07-27 20:51:31.776 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:51:31.777 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:51:41.955 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:51:41.955 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:51:42.190 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:51:42.190 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:51:42.191 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:51:42.192 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:51:42.365 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:51:42.365 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:51:44.404 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 使水平度盘水平
2025-07-27 20:51:44.406 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A使竖轴处于铅垂位置', 'B': 'B使水平度盘水平', 'C': 'C使横轴处于水平位置', 'D': 'D使竖轴位于竖直度盘铅垂面内', 'E': 'E使仪器中心与测站点标志中心位于同一铅垂线上'}
2025-07-27 20:51:44.406 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 使水平度盘水平
2025-07-27 20:51:44.407 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:51:44.407 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:51:44.408 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 15/28 个题目
2025-07-27 20:51:44.408 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 16/28 个题目
2025-07-27 20:51:44.410 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:51:44.411 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,这一操作不能消除或减弱的误差包括( )。...
2025-07-27 20:51:54.440 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:51:54.441 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,这一操作不能消除或减弱的误差包括( )。...
2025-07-27 20:51:54.715 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:51:54.716 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:51:54.716 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:51:54.717 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,...
2025-07-27 20:51:54.910 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:51:54.910 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:51:56.797 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 度盘刻划误差
仪器对中误差
2025-07-27 20:51:56.797 | INFO     | API.WorkTask:get_ai_answer_for_choice:1898 - ID:***********,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-27 20:51:56.798 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1969 - ID:***********,多选题匹配成功: A
2025-07-27 20:51:56.798 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 16/28 个题目
2025-07-27 20:51:56.798 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 17/28 个题目
2025-07-27 20:51:56.799 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:51:56.800 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部旋转方向说法正确的是( )。...
2025-07-27 20:52:06.981 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:52:06.981 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部旋转方向说法正确的是( )。...
2025-07-27 20:52:07.219 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:52:07.219 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:52:07.220 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:52:07.220 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部...
2025-07-27 20:52:07.420 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:52:07.420 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:52:08.840 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 盘左顺时针旋转
盘右逆时针旋转
2025-07-27 20:52:08.841 | INFO     | API.WorkTask:get_ai_answer_for_choice:1898 - ID:***********,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-27 20:52:08.841 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1969 - ID:***********,多选题匹配成功: A
2025-07-27 20:52:08.841 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 17/28 个题目
2025-07-27 20:52:08.841 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 18/28 个题目
2025-07-27 20:52:08.842 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:52:08.843 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:52:19.023 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:52:19.023 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:52:19.265 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:52:19.265 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:52:19.265 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:52:19.266 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:52:19.457 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:52:19.458 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:52:20.782 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_blank:2085 - ID:***********,AI生成填空题答案: 照准部###水平度盘###基座
2025-07-27 20:52:20.782 | SUCCESS  | API.WorkTask:Html_Wkrk:692 - ID:***********,AI生成填空题答案成功
2025-07-27 20:52:20.783 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 18/28 个题目
2025-07-27 20:52:20.783 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 19/28 个题目
2025-07-27 20:52:20.784 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:52:20.785 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:52:23.522 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:52:30.917 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:52:30.917 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:52:31.120 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:52:31.121 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:52:31.121 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:52:31.121 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:52:31.285 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:52:31.286 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:52:46.512 | WARNING  | API.WorkTask:_generate_ai_answer_for_blank:2088 - ID:***********,AI接口返回错误: API请求失败: HTTP码=0
2025-07-27 20:52:46.513 | WARNING  | API.WorkTask:Html_Wkrk:794 - ID:***********,AI答题失败，跳过此题
2025-07-27 20:52:46.513 | WARNING  | API.WorkTask:Html_Wkrk:811 - ID:***********,所有答题方法均失败，跳过此题
2025-07-27 20:52:46.513 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 20/28 个题目
2025-07-27 20:52:46.515 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:52:46.515 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步骤...
2025-07-27 20:52:56.669 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:52:56.669 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步骤...
2025-07-27 20:52:56.911 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:52:56.912 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:52:56.912 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:52:56.912 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步...
2025-07-27 20:52:57.093 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:52:57.094 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:52:58.736 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_blank:2085 - ID:***********,AI生成填空题答案: 对中###整平###瞄准###读数
2025-07-27 20:52:58.736 | SUCCESS  | API.WorkTask:Html_Wkrk:692 - ID:***********,AI生成填空题答案成功
2025-07-27 20:52:58.737 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 19/28 个题目
2025-07-27 20:52:58.737 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 21/28 个题目
2025-07-27 20:52:58.738 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:52:58.738 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:53:08.926 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:53:08.926 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:53:09.177 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:53:09.177 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:53:09.178 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:53:09.178 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:53:09.354 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:53:09.355 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:53:10.600 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 20:53:10.600 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 20/28 个题目
2025-07-27 20:53:10.601 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 22/28 个题目
2025-07-27 20:53:10.602 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:53:10.602 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 20:53:20.795 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:53:20.795 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 20:53:20.989 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:53:20.989 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:53:20.990 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:53:20.990 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加...
2025-07-27 20:53:21.176 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:53:21.177 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:53:22.832 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 20:53:22.833 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 21/28 个题目
2025-07-27 20:53:22.833 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 23/28 个题目
2025-07-27 20:53:22.834 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:53:22.835 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:53:23.523 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:53:32.981 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:53:32.981 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:53:33.240 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:53:33.240 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:53:33.241 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:53:33.241 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:53:33.436 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:53:33.437 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:53:35.736 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 使仪器中心与测站点位于同一铅垂线上。...
2025-07-27 20:53:35.737 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:53:35.737 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 22/28 个题目
2025-07-27 20:53:35.737 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 24/28 个题目
2025-07-27 20:53:35.738 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:53:35.738 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:53:45.909 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:53:45.909 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:53:46.139 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:53:46.139 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:53:46.140 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:53:46.140 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:53:46.332 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:53:46.333 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:53:47.969 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 使仪器竖轴处于铅垂位置，水平度盘处于水平状态。...
2025-07-27 20:53:47.969 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:53:47.970 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 23/28 个题目
2025-07-27 20:53:47.970 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 25/28 个题目
2025-07-27 20:53:47.971 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:53:47.971 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 测回法...
2025-07-27 20:53:58.134 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:53:58.134 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 测回法...
2025-07-27 20:53:58.359 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:53:58.359 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:53:58.360 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:53:58.360 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 测回法...
2025-07-27 20:53:58.533 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:53:58.534 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:54:00.306 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 测回法是一种水平角观测方法，通过盘左和盘右两个位置观测目标点，取平均值以提高精度。...
2025-07-27 20:54:00.306 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:54:00.307 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 24/28 个题目
2025-07-27 20:54:00.307 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 26/28 个题目
2025-07-27 20:54:00.308 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:54:00.308 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 盘左...
2025-07-27 20:54:10.474 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:54:10.474 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 盘左...
2025-07-27 20:54:10.674 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:54:10.674 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:54:10.674 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:54:10.675 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 盘左...
2025-07-27 20:54:10.854 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:54:10.855 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:54:13.114 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 观测时望远镜位于竖盘左侧的测量方式。...
2025-07-27 20:54:13.115 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:54:13.115 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 25/28 个题目
2025-07-27 20:54:13.115 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 27/28 个题目
2025-07-27 20:54:13.116 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:54:13.117 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 水平角...
2025-07-27 20:54:23.151 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:54:23.151 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 水平角...
2025-07-27 20:54:23.407 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:54:23.408 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:54:23.408 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:54:23.408 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 水平角...
2025-07-27 20:54:23.524 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:54:23.595 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:54:23.595 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:54:25.242 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 水平角是地面上一点到目标点的方向线在水平面上投影的夹角，用于测量和确定点的平面位置。...
2025-07-27 20:54:25.242 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:54:25.242 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 26/28 个题目
2025-07-27 20:54:25.243 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 28/28 个题目
2025-07-27 20:54:25.244 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:54:25.244 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:54:35.403 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:54:35.403 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:54:35.619 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:54:35.620 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:54:35.620 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:54:35.620 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:54:35.786 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:54:35.787 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:54:40.643 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2212 - ID:***********,AI生成简答题答案: 1. **安置仪器**：将经纬仪架设在三脚架上，调整脚架高度，确保仪器稳固且大致水平。  
2. *...
2025-07-27 20:54:40.644 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:54:40.644 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 27/28 个题目
2025-07-27 20:54:40.644 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 27/28 个
2025-07-27 20:54:40.644 | WARNING  | API.WorkTask:Html_Wkrk:857 - ID:***********,答题不完整(27/28)，使用保存模式
2025-07-27 20:54:41.100 | INFO     | API.WorkTask:PostDoChapterTest:1709 - ID:***********,保存模式请求URL: https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew?_classId=********&courseid=*********&token=c59d0bc0d30ca788c1835d425f9ec923&totalQuestionNum=c59d0bc0d30ca788c1835d425f9ec923&ua=pc&formType=post&saveStatus=1&version=1&tempsave=1
2025-07-27 20:54:41.101 | ERROR    | API.WorkTask:PostDoChapterTest:1716 - ID:***********,章节测验保存失败: {"msg":"无效的参数：code-2！","status":false}
2025-07-27 20:54:41.101 | ERROR    | API.WorkTask:PostDoChapterTest:1718 - ID:***********,保存请求URL: https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew?_classId=********&courseid=*********&token=c59d0bc0d30ca788c1835d425f9ec923&totalQuestionNum=c59d0bc0d30ca788c1835d425f9ec923&ua=pc&formType=post&saveStatus=1&version=1&tempsave=1
2025-07-27 20:54:41.102 | ERROR    | API.WorkTask:PostDoChapterTest:1719 - ID:***********,保存数据: {'pyFlag': '1', 'courseId': '*********', 'classId': ********, 'api': '1', 'workAnswerId': '53955060', 'answerId': '53955060', 'totalQuestionNum': 'c59d0bc0d30ca788c1835d425f9ec923', 'fullScore': '100.0', 'knowledgeid': '670797782', 'oldSchoolId': '', 'oldWorkId': '12674075eaee4aa3b9db01fdf31e0426', 'jobid': 'work-12674075eaee4aa3b9db01fdf31e0426', 'workRelationId': '32036431', 'enc': '', 'enc_work': 'c59d0bc0d30ca788c1835d425f9ec923', 'userId': '221880762', 'cpi': 282371395, 'workTimesEnc': '', 'randomOptions': 'false', 'isAccessibleCustomFid': '0', 'answer402242009': 'B', 'answer402242009_name': 'B-AI选择: B', 'answertype402242009': '0', 'answer402242010': 'A', 'answer402242010_name': 'A-AI选择: A', 'answertype402242010': '0', 'answer402242011': 'A', 'answer402242011_name': 'A-AI选择: A', 'answertype402242011': '0', 'answer402242012': 'B', 'answer402242012_name': 'B-AI选择: B', 'answertype402242012': '0', 'answer402242013': 'B', 'answer402242013_name': 'B-AI选择: B', 'answertype402242013': '0', 'answer402242014': 'B', 'answer402242014_name': 'B-AI选择: B', 'answertype402242014': '0', 'answer402242015': 'A', 'answer402242015_name': 'A-AI选择: A', 'answertype402242015': '0', 'answer402242016': 'B', 'answer402242016_name': 'B-AI选择: B', 'answertype402242016': '0', 'answer402242017': 'B', 'answer402242017_name': 'B-AI选择: B', 'answertype402242017': '0', 'answer402242018': 'A', 'answer402242018_name': 'A-AI选择: A', 'answertype402242018': '0', 'answer402242019': 'B', 'answer402242019_name': 'B-AI选择: B', 'answertype402242019': '0', 'answer402242020': 'B', 'answer402242020_name': 'B-AI选择: B', 'answertype402242020': '0', 'answer402242021': 'B', 'answer402242021_name': 'B-AI选择: B', 'answertype402242021': '0', 'answer402242022': 'B', 'answer402242022_name': 'B-AI选择: B', 'answertype402242022': '1', 'answer402242023': 'B', 'answer402242023_name': 'B-AI选择: B', 'answertype402242023': '1', 'answer402242024': 'A', 'answer402242024_name': 'A-AI选择: A', 'answertype402242024': '1', 'answer402242025': 'A', 'answer402242025_name': 'A-AI选择: A', 'answertype402242025': '1', 'tiankongsize402242026': '2', 'answertype402242026': '2', 'tiankongsize402242028': '2', 'answertype402242028': '2', 'answer402242029': 'true', 'answer402242029_name': 'true', 'answertype402242029': '3', 'answer402242030': 'false', 'answer402242030_name': 'false', 'answertype402242030': '3', 'answer402242031': '<p>使仪器中心与测站点位于同一铅垂线上。</p>', 'answer402242031_name': '<p>使仪器中心与测站点位于同一铅垂线上。</p>', 'answertype402242031': '4', 'answer402242032': '<p>使仪器竖轴处于铅垂位置，水平度盘处于水平状态。</p>', 'answer402242032_name': '<p>使仪器竖轴处于铅垂位置，水平度盘处于水平状态。</p>', 'answertype402242032': '4', 'answer402242033': '<p>测回法是一种水平角观测方法，通过盘左和盘右两个位置观测目标点，取平均值以提高精度。</p>', 'answer402242033_name': '<p>测回法是一种水平角观测方法，通过盘左和盘右两个位置观测目标点，取平均值以提高精度。</p>', 'answertype402242033': '5', 'answer402242034': '<p>观测时望远镜位于竖盘左侧的测量方式。</p>', 'answer402242034_name': '<p>观测时望远镜位于竖盘左侧的测量方式。</p>', 'answertype402242034': '5', 'answer402242035': '<p>水平角是地面上一点到目标点的方向线在水平面上投影的夹角，用于测量和确定点的平面位置。</p>', 'answer402242035_name': '<p>水平角是地面上一点到目标点的方向线在水平面上投影的夹角，用于测量和确定点的平面位置。</p>', 'answertype402242035': '5', 'answer402242036': '<p>1. **安置仪器**：将经纬仪架设在三脚架上，调整脚架高度，确保仪器稳固且大致水平。  \n2. **粗略整平**：通过调节三脚架腿使圆水准器气泡居中，初步调平仪器。  \n3. **精确整平**：使用脚螺旋调整管水准器，使长水准管气泡严格居中，确保仪器水平。  \n4. **对中**：平移或旋转基座，使光学对点器的中心标志与地面测站点重合，误差控制在允许范围内。  \n5. **瞄准目标**：松开水平与垂直制动螺旋，通过望远镜粗瞄目标，再调整物镜和目镜调焦使成像清晰，最后用微动螺旋精确对准目标。  \n6. **读数记录**：读取水平度盘和竖直度盘数值，记录数据，必要时重复测量以提高精度。  \n7. **收尾**：测量完成后，松开制动螺旋，将仪器复位并装箱。  \n\n（注：操作顺序可能因仪器型号或测量要求略有差异，需结合具体规范调整。）</p>', 'answer402242036_name': '<p>1. **安置仪器**：将经纬仪架设在三脚架上，调整脚架高度，确保仪器稳固且大致水平。  \n2. **粗略整平**：通过调节三脚架腿使圆水准器气泡居中，初步调平仪器。  \n3. **精确整平**：使用脚螺旋调整管水准器，使长水准管气泡严格居中，确保仪器水平。  \n4. **对中**：平移或旋转基座，使光学对点器的中心标志与地面测站点重合，误差控制在允许范围内。  \n5. **瞄准目标**：松开水平与垂直制动螺旋，通过望远镜粗瞄目标，再调整物镜和目镜调焦使成像清晰，最后用微动螺旋精确对准目标。  \n6. **读数记录**：读取水平度盘和竖直度盘数值，记录数据，必要时重复测量以提高精度。  \n7. **收尾**：测量完成后，松开制动螺旋，将仪器复位并装箱。  \n\n（注：操作顺序可能因仪器型号或测量要求略有差异，需结合具体规范调整。）</p>', 'answertype402242036': '6', 'answerwqbid': '402242009,402242010,402242011,402242012,402242013,402242014,402242015,402242016,402242017,402242018,402242019,402242020,402242021,402242022,402242023,402242024,402242025,402242026,402242028,402242029,402242030,402242031,402242032,402242033,402242034,402242035,402242036,'}
2025-07-27 20:54:45.605 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 3.6 单元测验 完成，等待 80 秒后处理下一章节
2025-07-27 20:55:23.526 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:56:06.205 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 20:56:07.289 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:5%,详情:课程任务:37/109 | 章节测验: 15/49; | 实时执行：4.1 光电量距 (2/34) | 更新:2025-07-27 20:56:05
2025-07-27 20:56:23.527 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:57:23.528 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:58:23.529 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:58:25.135 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 20:58:26.078 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 20:58:26.079 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 20:58:26.081 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到5个题目
2025-07-27 20:58:26.081 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/5 个题目
2025-07-27 20:58:26.082 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:58:26.082 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]有些全站仪在测量距离时,若不能设定仪器高和棱镜高(目标高),则所显示的高差值是( )与棱镜...
2025-07-27 20:58:33.055 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 全站仪横轴中心...
2025-07-27 20:58:33.056 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A全站仪横轴中心', 'B': 'B全站仪竖轴中心', 'C': 'C脚架中心', 'D': 'D全站仪照准轴'}
2025-07-27 20:58:33.057 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 全站仪横轴中心
2025-07-27 20:58:33.057 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 20:58:33.057 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: A
2025-07-27 20:58:33.058 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 1/5 个题目
2025-07-27 20:58:33.058 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/5 个题目
2025-07-27 20:58:33.059 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:58:33.059 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]电子测距仪的主要指标是( )。...
2025-07-27 20:58:40.227 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 测程和测距精度...
2025-07-27 20:58:40.228 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A测程和测距精度', 'B': 'B测距精度和测距模式', 'C': 'C测距范围和测距长短', 'D': 'D测距精度和测距可靠度'}
2025-07-27 20:58:40.228 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 测程和测距精度
2025-07-27 20:58:40.228 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 20:58:40.229 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: A
2025-07-27 20:58:40.229 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 2/5 个题目
2025-07-27 20:58:40.229 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/5 个题目
2025-07-27 20:58:40.231 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:58:40.231 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]根据光电测距仪测定时间方式的不同,光电测距仪可以分为( )两种类型...
2025-07-27 20:58:50.398 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:58:50.398 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]根据光电测距仪测定时间方式的不同,光电测距仪可以分为( )两种类型...
2025-07-27 20:58:50.672 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:58:50.672 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:58:50.673 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:58:50.673 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]根据光电测距仪测定时间方式的不同,光电测距仪可以分...
2025-07-27 20:58:50.866 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:58:50.866 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:58:52.417 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 脉冲式测距仪和相位式测距仪
2025-07-27 20:58:52.418 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A微波测距仪和激光测距仪', 'B': 'B脉冲式测距仪和相位式测距仪', 'C': 'C红外测距仪和光电测距仪', 'D': 'D光电测距仪和微波测距仪'}
2025-07-27 20:58:52.419 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 脉冲式测距仪和相位式测距仪
2025-07-27 20:58:52.419 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:58:52.419 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:58:52.419 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 3/5 个题目
2025-07-27 20:58:52.420 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/5 个题目
2025-07-27 20:58:52.421 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:58:52.421 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]关于光电测距的说法,错误的有( )。...
2025-07-27 20:58:57.375 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 镜站的后面应有反光源或强光源等背景不影响测距精度###强电磁场存在有利于提高测距精度...
2025-07-27 20:58:57.376 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A仪器长期不用时,应将电池取出。', 'B': 'B镜站的后面应有反光源或强光源等背景。', 'C': 'C大气条件稳定有利于提高测距精度。', 'D': 'D强电磁场存在有利于提高测距精度。', 'E': 'E测距时应防止阳光及其他强光直射接收物镜。'}
2025-07-27 20:58:57.376 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 镜站的后面应有反光源或强光源等背景不影响测距精度###强电磁场存在有利于提高测距精度
2025-07-27 20:58:57.377 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: D - 强电磁场存在有利于提高测距精度
2025-07-27 20:58:57.377 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: D
2025-07-27 20:58:57.377 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: D
2025-07-27 20:58:57.377 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 4/5 个题目
2025-07-27 20:58:57.378 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/5 个题目
2025-07-27 20:58:57.379 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:58:57.379 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]光电测距仪按照测定载波传播时间的方式不同可分为( )。...
2025-07-27 20:59:03.214 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 脉冲式测距仪###相位式测距仪...
2025-07-27 20:59:03.215 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A脉冲式测距仪', 'B': 'B相位式测距仪', 'C': 'C红外测距仪', 'D': 'D短程测距仪', 'E': 'E激光测距仪'}
2025-07-27 20:59:03.216 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 脉冲式测距仪###相位式测距仪
2025-07-27 20:59:03.216 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: A - 脉冲式测距仪
2025-07-27 20:59:03.217 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: B - 相位式测距仪
2025-07-27 20:59:03.217 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: AB
2025-07-27 20:59:03.217 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: AB
2025-07-27 20:59:03.217 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 5/5 个题目
2025-07-27 20:59:03.218 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 5/5 个
2025-07-27 20:59:03.218 | INFO     | API.WorkTask:Html_Wkrk:854 - ID:***********,答题完整，使用提交模式
2025-07-27 20:59:03.668 | SUCCESS  | API.WorkTask:PostDoChapterTest:1721 - ID:***********,章节测验提交成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=11dec18811a245b6831190be67c9f265&relationId=32036466&courseId=*********&clazzId=********&knowledgeId=670797765&mooc=0&jobId=work-a8ad3f5a38444685819417aeda819434&enc=7da22c092a47ef4e39b124874055bccb&ut=s&originJobId=null","status":true}
2025-07-27 20:59:12.033 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 4.1 光电量距 完成，等待 63 秒后处理下一章节
2025-07-27 20:59:23.530 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:00:15.613 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 21:00:16.672 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:8%,详情:课程任务:39/109 | 章节测验: 16/49; | 实时执行：4.2 视距测量 (3/34) | 更新:2025-07-27 21:00:15
2025-07-27 21:00:23.532 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:01:23.533 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:02:01.228 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 21:02:02.124 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 21:02:02.126 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 21:02:02.127 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到5个题目
2025-07-27 21:02:02.127 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/5 个题目
2025-07-27 21:02:02.129 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:02:02.129 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]视距测量时用望远镜内视距丝装臵,根据几何观测原理,同时测定两点间的( )的方法。...
2025-07-27 21:02:10.336 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 水平距离和高差...
2025-07-27 21:02:10.337 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A距离和高差', 'B': 'B水平距离和高差', 'C': 'C距离和高程', 'D': 'D倾斜距离和高差'}
2025-07-27 21:02:10.338 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 水平距离和高差
2025-07-27 21:02:10.338 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:02:10.338 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:02:10.338 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 1/5 个题目
2025-07-27 21:02:10.338 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/5 个题目
2025-07-27 21:02:10.340 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:02:10.340 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]视距测量的精度通常是( )。...
2025-07-27 21:02:12.251 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 低于钢尺...
2025-07-27 21:02:12.252 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A低于钢尺', 'B': 'B高于钢尺', 'C': 'C1/2000', 'D': 'D1/4000'}
2025-07-27 21:02:12.253 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 低于钢尺
2025-07-27 21:02:12.253 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 21:02:12.253 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: A
2025-07-27 21:02:12.253 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 2/5 个题目
2025-07-27 21:02:12.254 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/5 个题目
2025-07-27 21:02:12.255 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:02:12.256 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]用经纬仪进行视距测量,已知k=100,视距间隔为0.25,竖直角为+2°45′,则水平距离...
2025-07-27 21:02:19.710 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 24.94m...
2025-07-27 21:02:19.711 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A24.77m', 'B': 'B24.94m', 'C': 'C25.00m', 'D': 'D25.06m'}
2025-07-27 21:02:19.712 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 24.94m
2025-07-27 21:02:19.712 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:02:19.712 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:02:19.713 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 3/5 个题目
2025-07-27 21:02:19.713 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/5 个题目
2025-07-27 21:02:19.714 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:02:19.715 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]望远镜视线水平时,读的视距间隔为0.675m,则仪器至目标的水平距离为( )。...
2025-07-27 21:02:23.534 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:02:28.144 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 67.5m...
2025-07-27 21:02:28.145 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A0.675m', 'B': 'B6.75m', 'C': 'C67.5m', 'D': 'D675m'}
2025-07-27 21:02:28.145 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 67.5m
2025-07-27 21:02:28.146 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: C
2025-07-27 21:02:28.146 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: C
2025-07-27 21:02:28.146 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 4/5 个题目
2025-07-27 21:02:28.146 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/5 个题目
2025-07-27 21:02:28.147 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:02:28.148 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]用视距法测量地面两点之间的高差,需要观测的数据是( )。...
2025-07-27 21:02:35.587 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 上丝读数###中丝读数###下丝读数###仪器高...
2025-07-27 21:02:35.589 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A上丝读数', 'B': 'B中丝读数', 'C': 'C下丝读数', 'D': 'D仪器高', 'E': 'E水平度盘读数'}
2025-07-27 21:02:35.589 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 上丝读数###中丝读数###下丝读数###仪器高
2025-07-27 21:02:35.590 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: A - 上丝读数
2025-07-27 21:02:35.590 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: B - 中丝读数
2025-07-27 21:02:35.590 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: C - 下丝读数
2025-07-27 21:02:35.590 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: D - 仪器高
2025-07-27 21:02:35.591 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: ABCD
2025-07-27 21:02:35.591 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: ABCD
2025-07-27 21:02:35.591 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 5/5 个题目
2025-07-27 21:02:35.591 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 5/5 个
2025-07-27 21:02:35.592 | INFO     | API.WorkTask:Html_Wkrk:854 - ID:***********,答题完整，使用提交模式
2025-07-27 21:02:36.065 | SUCCESS  | API.WorkTask:PostDoChapterTest:1721 - ID:***********,章节测验提交成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=b420a1feb4c1462b8d43dff4bb115d3d&relationId=32036485&courseId=*********&clazzId=********&knowledgeId=670797766&mooc=0&jobId=work-3421007cc24f4f1184d56caf24f7ee86&enc=41e1613c4f80be02a1eab048f97cf43f&ut=s&originJobId=null","status":true}
2025-07-27 21:03:23.535 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:03:24.090 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 4.2 视距测量 完成，等待 85 秒后处理下一章节
2025-07-27 21:04:23.536 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:04:49.729 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 21:04:50.743 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:11%,详情:课程任务:42/109 | 章节测验: 17/49; | 实时执行：4.3 直线定向 (4/34) | 更新:2025-07-27 21:04:49
2025-07-27 21:05:23.537 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:06:23.537 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:06:37.765 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 21:06:38.713 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 21:06:38.714 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 21:06:38.715 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到5个题目
2025-07-27 21:06:38.715 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/5 个题目
2025-07-27 21:06:38.717 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:06:38.717 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]测量工作中,用以表示直线方向的象限角是由( )的北端或南端起,顺时针或逆时针至直线间所夹的...
2025-07-27 21:06:40.349 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 坐标纵轴方向...
2025-07-27 21:06:40.350 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A真子午线方向', 'B': 'B磁子午线方向', 'C': 'C坐标纵轴方向', 'D': 'D坐标横轴方向'}
2025-07-27 21:06:40.351 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 坐标纵轴方向
2025-07-27 21:06:40.351 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: C
2025-07-27 21:06:40.351 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: C
2025-07-27 21:06:40.352 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 1/5 个题目
2025-07-27 21:06:40.352 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/5 个题目
2025-07-27 21:06:40.353 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:06:40.354 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]用于测量内页计算的标准方向为(  )...
2025-07-27 21:06:47.054 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 坐标纵轴方向...
2025-07-27 21:06:47.055 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A真子午线方向', 'B': 'B磁子午线方向', 'C': 'C坐标纵轴方向', 'D': 'D高斯平面方向'}
2025-07-27 21:06:47.056 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 坐标纵轴方向
2025-07-27 21:06:47.056 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: C
2025-07-27 21:06:47.056 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: C
2025-07-27 21:06:47.056 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 2/5 个题目
2025-07-27 21:06:47.057 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/5 个题目
2025-07-27 21:06:47.058 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:06:47.058 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]第Ⅱ象限直线,象限角R与方位角α的关系为( )。...
2025-07-27 21:06:53.927 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: R=180°-α...
2025-07-27 21:06:53.928 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'AR=180°-α', 'B': 'BR=α', 'C': 'CR=α-180°', 'D': 'DR=360°-α'}
2025-07-27 21:06:53.928 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: R=180°-α
2025-07-27 21:06:53.929 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 21:06:53.929 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: A
2025-07-27 21:06:53.929 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 3/5 个题目
2025-07-27 21:06:53.929 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/5 个题目
2025-07-27 21:06:53.930 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:06:53.930 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]确定直线方向的标准方向有( )。...
2025-07-27 21:06:59.399 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 坐标纵轴方向###真子午线方向###磁子午线方向...
2025-07-27 21:06:59.400 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A中央子午线方向', 'B': 'B真子午线方向', 'C': 'C赤道方向', 'D': 'D磁子午线方向', 'E': 'E坐标纵轴方向'}
2025-07-27 21:06:59.401 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 坐标纵轴方向###真子午线方向###磁子午线方向
2025-07-27 21:06:59.401 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: E - 坐标纵轴方向
2025-07-27 21:06:59.401 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: B - 真子午线方向
2025-07-27 21:06:59.401 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: D - 磁子午线方向
2025-07-27 21:06:59.402 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: BDE
2025-07-27 21:06:59.402 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: BDE
2025-07-27 21:06:59.402 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 4/5 个题目
2025-07-27 21:06:59.402 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/5 个题目
2025-07-27 21:06:59.404 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:06:59.404 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]确定直线的方向,通常用该直线的( )来表示。...
2025-07-27 21:07:00.966 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 方位角###象限角...
2025-07-27 21:07:00.968 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A水平角', 'B': 'B方位角', 'C': 'C垂直角', 'D': 'D象限角'}
2025-07-27 21:07:00.968 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 方位角###象限角
2025-07-27 21:07:00.968 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: B - 方位角
2025-07-27 21:07:00.968 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: D - 象限角
2025-07-27 21:07:00.968 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: BD
2025-07-27 21:07:00.968 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: BD
2025-07-27 21:07:00.968 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 5/5 个题目
2025-07-27 21:07:00.969 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 5/5 个
2025-07-27 21:07:00.969 | INFO     | API.WorkTask:Html_Wkrk:854 - ID:***********,答题完整，使用提交模式
2025-07-27 21:07:01.419 | SUCCESS  | API.WorkTask:PostDoChapterTest:1721 - ID:***********,章节测验提交成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=0692b3687d9f4d1db45523cc47c29730&relationId=32036520&courseId=*********&clazzId=********&knowledgeId=670797767&mooc=0&jobId=work-879ef9de563f4cd9a9ebed50812cace3&enc=b520f23ce13e6e1fe1abe369f0a3ecd9&ut=s&originJobId=null","status":true}
2025-07-27 21:07:06.923 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 4.3 直线定向 完成，等待 67 秒后处理下一章节
2025-07-27 21:07:23.538 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:08:14.482 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 21:08:15.564 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:14%,详情:课程任务:44/109 | 章节测验: 18/49; | 实时执行：4.4 坐标方位角 (5/34) | 更新:2025-07-27 21:08:13
2025-07-27 21:08:23.539 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:09:23.540 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:10:23.541 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:10:32.110 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 21:10:33.042 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 21:10:33.043 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 21:10:33.044 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到5个题目
2025-07-27 21:10:33.045 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/5 个题目
2025-07-27 21:10:33.046 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:10:33.047 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]某直线的坐标方位角为121°23′36″,则反坐标方位角为( )。...
2025-07-27 21:10:34.949 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 301°23′36″...
2025-07-27 21:10:34.950 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A36″', 'B': 'B301°23′36″', 'C': 'C58°36′24″', 'D': 'D-58°36′24″'}
2025-07-27 21:10:34.951 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 301°23′36″
2025-07-27 21:10:34.951 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:10:34.951 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:10:34.952 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 1/5 个题目
2025-07-27 21:10:34.952 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/5 个题目
2025-07-27 21:10:34.953 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:10:34.954 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题].已知直线AB的坐标方位角为186°,则直线BA的坐标方位角为( )...
2025-07-27 21:10:38.949 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 6°...
2025-07-27 21:10:38.950 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A96', 'B': 'B276', 'C': 'C6', 'D': 'D186'}
2025-07-27 21:10:38.951 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 6°
2025-07-27 21:10:38.951 | INFO     | API.WorkTask:Xuan:1159 - ID:***********,尝试使用相似度匹配
2025-07-27 21:10:38.952 | INFO     | API.WorkTask:Xuan:1179 - ID:***********,尝试使用关键词匹配，答案关键词: {'6'}
2025-07-27 21:10:38.954 | INFO     | API.WorkTask:Xuan:1228 - ID:***********,尝试使用短语匹配，答案短语: ['6°']
2025-07-27 21:10:38.954 | ERROR    | API.WorkTask:Xuan:1336 - ID:***********,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 21:10:38.955 | WARNING  | API.WorkTask:Xuan:1340 - ID:***********,答案无法与任何选项匹配
2025-07-27 21:10:38.955 | WARNING  | API.WorkTask:Html_Wkrk:551 - ID:***********,主题库答案处理失败，尝试备用题库
2025-07-27 21:10:38.955 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 21:10:38.955 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题].已知直线AB的坐标方位角为186°,则直线BA的...
2025-07-27 21:10:39.237 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 21:10:39.238 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 21:10:40.815 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 6
2025-07-27 21:10:40.817 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A96', 'B': 'B276', 'C': 'C6', 'D': 'D186'}
2025-07-27 21:10:40.817 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 6
2025-07-27 21:10:40.817 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 21:10:40.818 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: A
2025-07-27 21:10:40.818 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 2/5 个题目
2025-07-27 21:10:40.818 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/5 个题目
2025-07-27 21:10:40.819 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:10:40.820 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]某直线的坐标方位角为163°50′36″,则其反坐标方位角为( )。...
2025-07-27 21:10:48.160 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 343°50′36″...
2025-07-27 21:10:48.162 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A253°50′36″', 'B': 'B196°09′24″', 'C': 'C-16°09′24″', 'D': 'D343°50′36″'}
2025-07-27 21:10:48.162 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 343°50′36″
2025-07-27 21:10:48.162 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: D
2025-07-27 21:10:48.163 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: D
2025-07-27 21:10:48.163 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 3/5 个题目
2025-07-27 21:10:48.163 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/5 个题目
2025-07-27 21:10:48.164 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:10:48.164 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]坐标方位角的特性有()...
2025-07-27 21:10:54.072 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 坐标方位角加上或减去360°n,所指方位不变###正反坐标方位角相差180°...
2025-07-27 21:10:54.073 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A坐标方位角加上或减去360°n,所指方位不变', 'B': 'B坐标方位角加上或减去360°n,所指方位相反', 'C': 'C正反坐标方位角相差180°', 'D': 'D正反坐标方位角相差180°-n'}
2025-07-27 21:10:54.074 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 坐标方位角加上或减去360°n,所指方位不变###正反坐标方位角相差180°
2025-07-27 21:10:54.074 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: A - 坐标方位角加上或减去360°n,所指方位不变
2025-07-27 21:10:54.075 | INFO     | API.WorkTask:Xuan:1071 - ID:***********,多选题部分包含匹配: C - 正反坐标方位角相差180°
2025-07-27 21:10:54.075 | SUCCESS  | API.WorkTask:Xuan:1095 - ID:***********,多选题匹配成功: AC
2025-07-27 21:10:54.075 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: AC
2025-07-27 21:10:54.075 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 4/5 个题目
2025-07-27 21:10:54.076 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/5 个题目
2025-07-27 21:10:54.077 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:10:54.077 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 直线方位角与该直线的反方位角相差度...
2025-07-27 21:11:04.257 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 21:11:04.257 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 直线方位角与该直线的反方位角相差度...
2025-07-27 21:11:04.528 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 21:11:04.528 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 21:11:04.528 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 21:11:04.529 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 直线方位角与该直线的反方位角相差度...
2025-07-27 21:11:04.695 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 21:11:04.695 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 21:11:06.803 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 21:11:06.804 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 5/5 个题目
2025-07-27 21:11:06.804 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 5/5 个
2025-07-27 21:11:06.804 | INFO     | API.WorkTask:Html_Wkrk:854 - ID:***********,答题完整，使用提交模式
2025-07-27 21:11:07.287 | SUCCESS  | API.WorkTask:PostDoChapterTest:1721 - ID:***********,章节测验提交成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=762de056875e497793b85c9457c49dae&relationId=********&courseId=*********&clazzId=********&knowledgeId=*********&mooc=0&jobId=work-a9866f9bee694b008a97f97e84fceaa9&enc=0f8be7e6edb9ef22cbd35565c4d43d9b&ut=s&originJobId=null","status":true}
2025-07-27 21:11:12.791 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 4.4 坐标方位角 完成，等待 88 秒后处理下一章节
2025-07-27 21:11:23.542 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:12:23.544 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:31:52.741 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:31:54.073 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 21:31:57.452 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:17%,详情:课程任务:46/109 | 章节测验: 19/49; | 实时执行：4.5 单元测验 (6/34) | 更新:2025-07-27 21:31:52
2025-07-27 21:32:02.399 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 21:32:06.226 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 21:32:06.227 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 21:32:06.231 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到19个题目
2025-07-27 21:32:06.231 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/19 个题目
2025-07-27 21:32:06.233 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:06.234 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]电子测距仪的主要指标是( )。...
2025-07-27 21:32:12.958 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 测程和测距精度...
2025-07-27 21:32:12.959 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A测程和测距精度', 'B': 'B测距精度和测距模式', 'C': 'C测距范围和测距长短', 'D': 'D测距精度和测距可靠度'}
2025-07-27 21:32:12.960 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 测程和测距精度
2025-07-27 21:32:12.960 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 21:32:12.960 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: A
2025-07-27 21:32:12.960 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 1/19 个题目
2025-07-27 21:32:12.961 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/19 个题目
2025-07-27 21:32:12.962 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:12.962 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]视距测量时用望远镜内视距丝装臵,根据几何观测原理,同时测定两点间的( )的方法。...
2025-07-27 21:32:19.077 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 水平距离和高差...
2025-07-27 21:32:19.079 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A距离和高差', 'B': 'B水平距离和高差', 'C': 'C距离和高程', 'D': 'D倾斜距离和高差'}
2025-07-27 21:32:19.079 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 水平距离和高差
2025-07-27 21:32:19.080 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:32:19.080 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:32:19.080 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 2/19 个题目
2025-07-27 21:32:19.080 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/19 个题目
2025-07-27 21:32:19.082 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:19.082 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]望远镜视线水平时,读的视距间隔为0.675m,则仪器至目标的水平距离为( )。...
2025-07-27 21:32:26.799 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 67.5m...
2025-07-27 21:32:26.800 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A0.675m', 'B': 'B6.75m', 'C': 'C67.5m', 'D': 'D675m'}
2025-07-27 21:32:26.801 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 67.5m
2025-07-27 21:32:26.801 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: C
2025-07-27 21:32:26.801 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: C
2025-07-27 21:32:26.802 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 3/19 个题目
2025-07-27 21:32:26.802 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/19 个题目
2025-07-27 21:32:26.804 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:26.804 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]用经纬仪进行视距测量,已知k=100,视距间隔为0.25,竖直角为+2°45′,则水平距离...
2025-07-27 21:32:35.263 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 24.94m...
2025-07-27 21:32:35.266 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A24.77m', 'B': 'B24.94m', 'C': 'C25.00m', 'D': 'D25.06m'}
2025-07-27 21:32:35.266 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 24.94m
2025-07-27 21:32:35.267 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:32:35.268 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:32:35.268 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 4/19 个题目
2025-07-27 21:32:35.269 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/19 个题目
2025-07-27 21:32:35.270 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:35.270 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]直线的坐标方位角是按( )方式量取的。...
2025-07-27 21:32:45.465 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 21:32:45.466 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]直线的坐标方位角是按( )方式量取的。...
2025-07-27 21:32:45.763 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 21:32:45.763 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 21:32:45.763 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 21:32:45.765 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]直线的坐标方位角是按( )方式量取的。...
2025-07-27 21:32:45.929 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 21:32:45.930 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 21:32:47.452 | INFO     | API.WorkTask:get_ai_answer_for_choice:1893 - ID:***********,AI返回的答案内容: 坐标纵轴北端顺时针
2025-07-27 21:32:47.456 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A坐标纵轴北端起逆时针', 'B': 'B坐标横轴东端逆时针', 'C': 'C坐标纵轴北端顺时针', 'D': 'D坐标横轴东端顺时针'}
2025-07-27 21:32:47.457 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 坐标纵轴北端顺时针
2025-07-27 21:32:47.458 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: C
2025-07-27 21:32:47.459 | INFO     | API.WorkTask:get_ai_answer_for_choice:1985 - ID:***********,使用Xuan函数匹配成功: C
2025-07-27 21:32:47.460 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 5/19 个题目
2025-07-27 21:32:47.461 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 6/19 个题目
2025-07-27 21:32:47.464 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:47.465 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]某直线的象限角为北西35°,则其反坐标方位角为( )。...
2025-07-27 21:32:52.742 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 21:32:55.830 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
2025-07-27 21:32:57.006 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 145°...
2025-07-27 21:32:57.010 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A35°', 'B': 'B145°', 'C': 'C215°', 'D': 'D325°'}
2025-07-27 21:32:57.010 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 145°
2025-07-27 21:32:57.012 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 21:32:57.012 | SUCCESS  | API.WorkTask:Html_Wkrk:489 - ID:***********,主题库答案匹配成功: B
2025-07-27 21:32:57.012 | INFO     | API.WorkTask:Html_Wkrk:546 - ID:***********,已处理 6/19 个题目
2025-07-27 21:32:57.012 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 7/19 个题目
2025-07-27 21:32:57.013 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 21:32:57.014 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]电磁波测距的优点有( )。...
