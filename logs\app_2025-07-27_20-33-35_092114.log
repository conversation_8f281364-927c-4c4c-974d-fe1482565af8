2025-07-27 20:33:35.093 | INFO     | __main__:<module>:1894 - 学习通自动化系统启动...
2025-07-27 20:33:35.093 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-27 20:33:35.093 | SUCCESS  | __main__:<module>:1904 - 数据库连接池初始化成功
2025-07-27 20:33:35.097 | SUCCESS  | __main__:<module>:1925 - 数据状态重置完成
2025-07-27 20:33:35.097 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 0/100
2025-07-27 20:33:35.097 | INFO     | __main__:<module>:1941 - 开始处理订单...
2025-07-27 20:33:35.097 | INFO     | __main__:order_get:1697 - 订单处理线程启动，最大线程数: 100
2025-07-27 20:33:35.100 | INFO     | __main__:order_get:1722 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-27 20:33:35.100 | INFO     | __main__:Run:1781 - 开始处理订单: OID=297357, 用户=***********, 课程ID=229349196
2025-07-27 20:33:35.100 | INFO     | __main__:register_thread:67 - 注册线程: OID=297357, 当前活跃线程数: 1
2025-07-27 20:33:35.628 | SUCCESS  | __main__:Run:1825 - ID:***********,登录成功
2025-07-27 20:33:35.636 | INFO     | __main__:kclist:254 - ID:***********,正在获取课程列表...
2025-07-27 20:33:36.183 | INFO     | __main__:kclist:256 - ID:***********,课程列表API返回: {'result': 1, 'msg': '获取成功', 'channelList': [{'cfid': -1, 'norder': 72, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108147120, 'content': {'studentcount': 35, 'chatid': '262452157677570', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王欣欣', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=247101786&personId=282371395&classId=108147120&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '建筑设备与识图', 'id': 247101786}]}, 'roletype': 3, 'id': 108147120, 'state': 0, 'cpi': 282371395, 'bbsid': '233d0c9384c7fd49ef30471e70b6f18d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 70, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 103243606, 'content': {'studentcount': 37, 'chatid': '257804644450310', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '9136', 'coursestate': 0, 'teacherfactor': '辛星、孙万香', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245076370&personId=306706095&classId=103243606&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '现代建筑施工项目管理', 'id': 245076370}]}, 'roletype': 3, 'id': 103243606, 'state': 0, 'cpi': 306706095, 'bbsid': '82e9a0452a5fb0153a08742d38d2e5d3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 62, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93174104, 'content': {'studentcount': 36, 'chatid': '240755038814211', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=240249074&personId=282371395&classId=93174104&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '装配式构造与识图', 'id': 240249074}]}, 'roletype': 3, 'id': 93174104, 'state': 0, 'cpi': 282371395, 'bbsid': '9c443055acdff2d922c91a98f0d01497', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 61, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93031937, 'content': {'studentcount': 37, 'chatid': '240686109622276', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '乔颖秀', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241048997&personId=282371395&classId=93031937&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '建筑装饰施工技术', 'id': 241048997}]}, 'roletype': 3, 'id': 93031937, 'state': 0, 'cpi': 282371395, 'bbsid': '8bc656572d3aeb51ff07fd8df2167bde', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 58, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 91297170, 'content': {'studentcount': 36, 'chatid': '235813675794433', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '6,9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229349196&personId=282371395&classId=91297170&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/b34d7695e8fd9ce297ef09b2bcb31608.jpg', 'name': '工程测量', 'id': 229349196}]}, 'roletype': 3, 'id': 91297170, 'state': 0, 'cpi': 282371395, 'bbsid': 'bee20260b71f18a382f79be093c75781', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 50, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 84338229, 'content': {'studentcount': 36, 'chatid': '228028031500290', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵翠', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237732213&personId=282371395&classId=84338229&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '监理概论', 'id': 237732213}]}, 'roletype': 3, 'id': 84338229, 'state': 0, 'cpi': 282371395, 'bbsid': 'c7b537a1f1f64b3e1f029c0b1e6f3ac9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 41, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81695272, 'content': {'studentcount': 39, 'chatid': '225435820556293', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=222501937&personId=282371395&classId=81695272&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/02dd581c5cfd7d4840aa0924a5741b87.png', 'name': '砌体工程施工', 'id': 222501937}]}, 'roletype': 3, 'id': 81695272, 'state': 0, 'cpi': 282371395, 'bbsid': '50a0052e1faaf398eab9eed65fdcbbe6', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 34, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77731070, 'content': {'studentcount': 37, 'chatid': '214216739979265', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=206940210&personId=282371395&classId=77731070&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/5fcb560ba7bde03929ccae19113a260a.png', 'name': '房屋构造', 'id': 206940210}]}, 'roletype': 3, 'id': 77731070, 'state': 0, 'cpi': 282371395, 'bbsid': '727dcfd2ef24a37e905835bc220b2e79', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 29, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 73006297, 'content': {'studentcount': 36, 'chatid': '207071973801986', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232937843&personId=282371395&classId=73006297&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/445cfff6239083f79374551111fb385b.png', 'name': '二十四式简化太极拳', 'id': 232937843}]}, 'roletype': 3, 'id': 73006297, 'state': 0, 'cpi': 282371395, 'bbsid': 'bd43bf14e2bae80865bad5e04a164058', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 24, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63861990, 'content': {'studentcount': 56, 'chatid': '193216339050497', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102、装配22101', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王波', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228707968&personId=282371395&classId=63861990&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '应用数学A（一）', 'id': 228707968}]}, 'roletype': 3, 'id': 63861990, 'state': 0, 'cpi': 282371395, 'bbsid': 'd879253745a36fc8ffa778689174eb5f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 13, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64051718, 'content': {'studentcount': 38, 'chatid': '193380112990210', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张睿', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228856150&personId=282371395&classId=64051718&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/ee1c91e5d692ad719f7edd5c90a3625e.jpg', 'name': '2022级大学英语', 'id': 228856150}]}, 'roletype': 3, 'id': 64051718, 'state': 0, 'cpi': 282371395, 'bbsid': '1df2bd032967a8bdb8a0185c961aceff', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 12, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63990559, 'content': {'studentcount': 37, 'chatid': '193312997834753', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黎佳媚', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228506578&personId=239575512&classId=63990559&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生心理健康', 'id': 228506578}]}, 'roletype': 3, 'id': 63990559, 'state': 0, 'cpi': 239575512, 'bbsid': '63e85270f677fb0d9ca5bc5a66abcf82', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 11, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63680895, 'content': {'studentcount': 37, 'chatid': '193107346915329', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=219619084&personId=239575512&classId=63680895&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑力学与结构', 'id': 219619084}]}, 'roletype': 3, 'id': 63680895, 'state': 0, 'cpi': 239575512, 'bbsid': '83e4bf9de026eecc5d2c0344a0d72062', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 10, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63759655, 'content': {'studentcount': 36, 'chatid': '193145009668098', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '崔炳文', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228721228&personId=239575512&classId=63759655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生职业发展与就业指导', 'id': 228721228}]}, 'roletype': 3, 'id': 63759655, 'state': 0, 'cpi': 239575512, 'bbsid': '4247ba6c3c91d5fe2b0847f4fb2dab18', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 3, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63765619, 'content': {'studentcount': 36, 'chatid': '193151980601345', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228724038&personId=282371395&classId=63765619&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/4d22019e5c81452b5fc4082ac48a0fc0.png', 'name': '体育与健康课-西职院-建筑与轨道交通学院2022级', 'id': 228724038}]}, 'roletype': 3, 'id': 63765619, 'state': 0, 'cpi': 282371395, 'bbsid': 'bfb1bf61cb93753dc55127646480fb23', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 2, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64009643, 'content': {'studentcount': 35, 'chatid': '193329581064194', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王觅', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228836418&personId=282371395&classId=64009643&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑工程识图（1+X）', 'id': 228836418}]}, 'roletype': 3, 'id': 64009643, 'state': 0, 'cpi': 282371395, 'bbsid': 'c974502359bdde67e6a49c879e1c5a4c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 71, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108143976, 'content': {'studentcount': 38, 'chatid': '262448649142274', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月  王成平 吉海军 孟琳 牛欣欣       西安职业技术学院', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245183825&personId=282371395&classId=108143976&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/00ceb684f7b286cb38e7a610330ee3bb.png', 'name': '建筑类专业创新创业基础', 'id': 245183825}]}, 'roletype': 3, 'id': 108143976, 'state': 1, 'cpi': 282371395, 'bbsid': 'a61bd4f169036c406137776a7e69edd4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 67, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 99702566, 'content': {'studentcount': 31, 'chatid': '249557002813441', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙万香、张阳、乔颖秀，李頔、刘颖、伦琳琳、吕萍', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=242066105&personId=282371395&classId=99702566&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/e15f9d0fa84e0dc88cf6bee453692061.jpg', 'name': '居住空间设计', 'id': 242066105}]}, 'roletype': 3, 'id': 99702566, 'state': 1, 'cpi': 282371395, 'bbsid': 'f7f88c1f5d8e2510715d9d40c0f52c11', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 66, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 98878754, 'content': {'studentcount': 36, 'chatid': '247959327408129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241528817&personId=282371395&classId=98878754&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 241528817}]}, 'roletype': 3, 'id': 98878754, 'state': 1, 'cpi': 282371395, 'bbsid': 'bc75f264d544b6db51bc97e06a2dbffb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 65, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 96477120, 'content': {'studentcount': 66, 'chatid': '243660016910338', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22101班+22102班（张莹霞）', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '高静娜王世伟王颖易云王虹田雨李芮王怡玮张莹霞何慧琦刘景华马岩李晓航范建梅李殷红郭炯张哲王帅军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241400281&personId=282371395&classId=96477120&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/411a62e07ee35beb471ff8591e7c0583.png', 'name': '习近平新时代中国特色社会主义思想概论', 'id': 241400281}]}, 'roletype': 3, 'id': 96477120, 'state': 1, 'cpi': 282371395, 'bbsid': 'a31e26a3f7dcfe346e427926a0c12843', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 64, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 94747290, 'content': {'studentcount': 36, 'chatid': '241659454488581', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241352309&personId=282371395&classId=94747290&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 241352309}]}, 'roletype': 3, 'id': 94747290, 'state': 1, 'cpi': 282371395, 'bbsid': '8615728073e0266575a4f2634f73f4b4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 63, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93458508, 'content': {'studentcount': 37, 'chatid': '240865358446597', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王成平', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241226635&personId=282371395&classId=93458508&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0078f13df62d385adbf64bd357801250.jpg', 'name': '装配式建筑构件制作与安装（1+X）', 'id': 241226635}]}, 'roletype': 3, 'id': 93458508, 'state': 1, 'cpi': 282371395, 'bbsid': '54f658b680b207e33f03dc30f26eaaf9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 57, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 90776227, 'content': {'studentcount': 36, 'chatid': '235091979730946', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=239229163&personId=282371395&classId=90776227&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 239229163}]}, 'roletype': 3, 'id': 90776227, 'state': 1, 'cpi': 282371395, 'bbsid': '3fa527de268d07e7f56fd000639a6280', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 56, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86627150, 'content': {'studentcount': 39, 'chatid': '230251015766019', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257713&personId=282371395&classId=86627150&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 236257713}]}, 'roletype': 3, 'id': 86627150, 'state': 1, 'cpi': 282371395, 'bbsid': 'c9e6fc8ae6f2a130c48c7e7781f42187', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 55, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86450001, 'content': {'studentcount': 36, 'chatid': '230515973095428', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257673&personId=282371395&classId=86450001&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 236257673}]}, 'roletype': 3, 'id': 86450001, 'state': 1, 'cpi': 282371395, 'bbsid': 'ccf2d9d3dd2cdde64beec68109dea4b0', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 53, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568471, 'content': {'studentcount': 184, 'chatid': '226902282403844', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黄玉顺', 'isCourseSquare': 0, 'schools': '山东大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417781&personId=282371395&classId=83568471&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cac10875886f12e31fa939a4b330acc2.jpg', 'name': '儒学与生活', 'id': 237417781}]}, 'roletype': 3, 'id': 83568471, 'state': 1, 'cpi': 282371395, 'bbsid': 'd5b6945eac682309e1c07aaf36380f67', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 52, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568466, 'content': {'studentcount': 84, 'chatid': '226902281355269', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙劲松 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417778&personId=282371395&classId=83568466&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cdcd8e1a0722b927b2bd2370ab0ca37f.jpg', 'name': '《周易》的奥秘', 'id': 237417778}]}, 'roletype': 3, 'id': 83568466, 'state': 1, 'cpi': 282371395, 'bbsid': '3905ad7fe8b5446aa42aba53526e017c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 51, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568419, 'content': {'studentcount': 186, 'chatid': '226902271918084', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李义平', 'isCourseSquare': 0, 'schools': '中国人民大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417727&personId=282371395&classId=83568419&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star/270_169c/1395222258227jqpbs.png', 'name': '经济学百年', 'id': 237417727}]}, 'roletype': 3, 'id': 83568419, 'state': 1, 'cpi': 282371395, 'bbsid': '8c3ac1bcb7e980cd757784e60d19c2f1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 48, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83894032, 'content': {'studentcount': 34, 'chatid': '227791773696001', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '袁敏、李缪美、冯三琴、陈萌、赵亚楠', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236205491&personId=282371395&classId=83894032&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/2b4f778a10dc049954c0703905744ed6.jpg', 'name': '中华优秀传统文化', 'id': 236205491}]}, 'roletype': 3, 'id': 83894032, 'state': 1, 'cpi': 282371395, 'bbsid': '2ef8c7abe16df4475e4c3608820689e1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 47, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83565558, 'content': {'studentcount': 3241, 'chatid': '226899079004165', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '刘剑 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363016&personId=282371395&classId=83565558&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/033b195bb4d859c34f837436a2e2b047.png', 'name': '职业生涯提升', 'id': 237363016}]}, 'roletype': 3, 'id': 83565558, 'state': 1, 'cpi': 282371395, 'bbsid': 'da7fb09f3a83f26e851457f5b6c7139c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 46, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83563993, 'content': {'studentcount': 3241, 'chatid': '226898253774851', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363015&personId=282371395&classId=83563993&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 237363015}]}, 'roletype': 3, 'id': 83563993, 'state': 1, 'cpi': 282371395, 'bbsid': '32e5fc34f009a71da5533841a69b8d4f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 44, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 82934974, 'content': {'studentcount': 37, 'chatid': '226347724111878', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236255632&personId=282371395&classId=82934974&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 236255632}]}, 'roletype': 3, 'id': 82934974, 'state': 1, 'cpi': 282371395, 'bbsid': '0930cc6c545794f09023829842e80bbb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 42, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81792501, 'content': {'studentcount': 40, 'chatid': '225462012936193', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225074231&personId=282371395&classId=81792501&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ccf2e893a0fec74beb2c807bed1380fe.png', 'name': '钢筋混凝土工程施工', 'id': 225074231}]}, 'roletype': 3, 'id': 81792501, 'state': 1, 'cpi': 282371395, 'bbsid': 'aab65d5d802d23a526d71e6cf24fa6ec', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 40, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 79340160, 'content': {'studentcount': 306, 'chatid': '218929127555073', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2023春校内选课4班', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '关蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232872307&personId=306706095&classId=79340160&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/45d95091261bf5413d34711b4812f878.png', 'name': '城市轨道交通行车组织', 'id': 232872307}]}, 'roletype': 3, 'id': 79340160, 'state': 1, 'cpi': 306706095, 'bbsid': 'ada486380464473062934e91bb473e95', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 35, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 78162387, 'content': {'studentcount': 101, 'chatid': '215496757673985', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '1+X培训', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=78162387&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 78162387, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 33, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77274590, 'content': {'studentcount': 36, 'chatid': '212705581531137', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=77274590&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 77274590, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 31, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74231723, 'content': {'studentcount': 37, 'chatid': '208325644976129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233334480&personId=282371395&classId=74231723&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 233334480}]}, 'roletype': 3, 'id': 74231723, 'state': 1, 'cpi': 282371395, 'bbsid': 'ac44ce09c57beb7098f3962568c9b5da', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 30, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74122655, 'content': {'studentcount': 38, 'chatid': '208076318769153', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233076005&personId=282371395&classId=74122655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 233076005}]}, 'roletype': 3, 'id': 74122655, 'state': 1, 'cpi': 282371395, 'bbsid': 'e18b21fa283bde3c284082489b2e2d9d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 28, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 72909517, 'content': {'studentcount': 42, 'chatid': '207009144176641', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232915084&personId=282371395&classId=72909517&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/8da13f152a75aa27fd646b620d4a785c.jpg', 'name': 'BIM技术Revit软件建模', 'id': 232915084}]}, 'roletype': 3, 'id': 72909517, 'state': 1, 'cpi': 282371395, 'bbsid': '37149f94ac3f555df5c8e9f9e7bba0c3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 20, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 69595761, 'content': {'studentcount': 37, 'chatid': '200442127646722', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225425874&personId=239575512&classId=69595761&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 225425874}]}, 'roletype': 3, 'id': 69595761, 'state': 1, 'cpi': 239575512, 'bbsid': 'b83a68cde1f0614c47b8e2cd2d0b6ccf', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 19, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66385034, 'content': {'studentcount': 103, 'chatid': '196295283245059', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '杨树山', 'isCourseSquare': 0, 'schools': '天津理工大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229863793&personId=282371395&classId=66385034&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/56d95359e4b0dfadae7a3f77.jpg', 'name': '漫画艺术欣赏与创作', 'id': 229863793}]}, 'roletype': 3, 'id': 66385034, 'state': 1, 'cpi': 282371395, 'bbsid': 'cee2bff5ffef3ec232d3843cdc413c6f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 18, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66384671, 'content': {'studentcount': 50, 'chatid': '196295121764358', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '段鑫星', 'isCourseSquare': 0, 'schools': '中国矿业大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229864935&personId=282371395&classId=66384671&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6a97d52937f928f858572fa3d0cc9e9a.jpg', 'name': '恋爱心理学', 'id': 229864935}]}, 'roletype': 3, 'id': 66384671, 'state': 1, 'cpi': 282371395, 'bbsid': 'd269da8fd0755fc780863bd28fd29ff4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 17, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115796, 'content': {'studentcount': 3586, 'chatid': '195917631258628', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张国清', 'isCourseSquare': 0, 'schools': '同济大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745592&personId=282371395&classId=66115796&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/f01bc30632e023f83b3e8879cdeea2c7.jpg', 'name': '军事理论', 'id': 229745592}]}, 'roletype': 3, 'id': 66115796, 'state': 1, 'cpi': 282371395, 'bbsid': '2d62f1844657fabcd6f9a66200efc9ce', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 16, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115802, 'content': {'studentcount': 3268, 'chatid': '195917634404353', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745593&personId=282371395&classId=66115802&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 229745593}]}, 'roletype': 3, 'id': 66115802, 'state': 1, 'cpi': 282371395, 'bbsid': '3294c8c4ccdf002e427fba19c732b9c8', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 15, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115811, 'content': {'studentcount': 3268, 'chatid': '195917639647234', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '齐晖 等', 'isCourseSquare': 0, 'schools': '中原工学院', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745598&personId=282371395&classId=66115811&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/560a0b0ee4b040cfea1a0f68.jpg', 'name': '大学计算机基础', 'id': 229745598}]}, 'roletype': 3, 'id': 66115811, 'state': 1, 'cpi': 282371395, 'bbsid': '015404e051dbfe6a573acaa45e6b29dc', 'isSquare': 0}, 'topsign': 0}], 'mcode': '-1', 'createcourse': 1, 'teacherEndCourse': 0, 'showEndCourse': 1, 'hasMore': False, 'stuEndCourse': 1}
2025-07-27 20:33:36.192 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 247101786, 目标课程ID: 229349196
2025-07-27 20:33:36.193 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245076370, 目标课程ID: 229349196
2025-07-27 20:33:36.193 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 240249074, 目标课程ID: 229349196
2025-07-27 20:33:36.193 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241048997, 目标课程ID: 229349196
2025-07-27 20:33:36.193 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229349196, 目标课程ID: 229349196
2025-07-27 20:33:36.193 | SUCCESS  | __main__:kclist:298 - ID:***********,成功匹配课程: {'kcname': '工程测量', 'courseid': '229349196', 'clazzid': 91297170, 'cpi': 282371395}
2025-07-27 20:33:36.194 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237732213, 目标课程ID: 229349196
2025-07-27 20:33:36.194 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 222501937, 目标课程ID: 229349196
2025-07-27 20:33:36.194 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 206940210, 目标课程ID: 229349196
2025-07-27 20:33:36.194 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232937843, 目标课程ID: 229349196
2025-07-27 20:33:36.195 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228707968, 目标课程ID: 229349196
2025-07-27 20:33:36.195 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228856150, 目标课程ID: 229349196
2025-07-27 20:33:36.195 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228506578, 目标课程ID: 229349196
2025-07-27 20:33:36.195 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 219619084, 目标课程ID: 229349196
2025-07-27 20:33:36.195 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228721228, 目标课程ID: 229349196
2025-07-27 20:33:36.196 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228724038, 目标课程ID: 229349196
2025-07-27 20:33:36.196 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228836418, 目标课程ID: 229349196
2025-07-27 20:33:36.196 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245183825, 目标课程ID: 229349196
2025-07-27 20:33:36.197 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 242066105, 目标课程ID: 229349196
2025-07-27 20:33:36.197 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241528817, 目标课程ID: 229349196
2025-07-27 20:33:36.197 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241400281, 目标课程ID: 229349196
2025-07-27 20:33:36.197 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241352309, 目标课程ID: 229349196
2025-07-27 20:33:36.198 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241226635, 目标课程ID: 229349196
2025-07-27 20:33:36.198 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 239229163, 目标课程ID: 229349196
2025-07-27 20:33:36.198 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257713, 目标课程ID: 229349196
2025-07-27 20:33:36.198 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257673, 目标课程ID: 229349196
2025-07-27 20:33:36.199 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417781, 目标课程ID: 229349196
2025-07-27 20:33:36.199 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417778, 目标课程ID: 229349196
2025-07-27 20:33:36.199 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417727, 目标课程ID: 229349196
2025-07-27 20:33:36.199 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236205491, 目标课程ID: 229349196
2025-07-27 20:33:36.200 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363016, 目标课程ID: 229349196
2025-07-27 20:33:36.200 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363015, 目标课程ID: 229349196
2025-07-27 20:33:36.200 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236255632, 目标课程ID: 229349196
2025-07-27 20:33:36.200 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225074231, 目标课程ID: 229349196
2025-07-27 20:33:36.200 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232872307, 目标课程ID: 229349196
2025-07-27 20:33:36.201 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: 229349196
2025-07-27 20:33:36.201 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: 229349196
2025-07-27 20:33:36.201 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233334480, 目标课程ID: 229349196
2025-07-27 20:33:36.201 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233076005, 目标课程ID: 229349196
2025-07-27 20:33:36.201 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232915084, 目标课程ID: 229349196
2025-07-27 20:33:36.202 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225425874, 目标课程ID: 229349196
2025-07-27 20:33:36.202 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229863793, 目标课程ID: 229349196
2025-07-27 20:33:36.202 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229864935, 目标课程ID: 229349196
2025-07-27 20:33:36.202 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745592, 目标课程ID: 229349196
2025-07-27 20:33:36.203 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745593, 目标课程ID: 229349196
2025-07-27 20:33:36.203 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745598, 目标课程ID: 229349196
2025-07-27 20:33:36.203 | SUCCESS  | __main__:Run:1834 - ID:***********,课程信息匹配成功
2025-07-27 20:33:36.925 | SUCCESS  | __main__:studentcourse:757 - ID:***********,课件获取完成，总共获取到34个未完成章节
2025-07-27 20:33:37.577 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 20:33:38.713 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:2%,详情:课程任务:37/109 | 章节测验: 15/49; | 实时执行：3.6 单元测验 (1/34) | 更新:2025-07-27 20:33:36
2025-07-27 20:33:40.845 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 20:33:42.425 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 20:33:42.426 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 20:33:42.433 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到28个题目
2025-07-27 20:33:42.434 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/28 个题目
2025-07-27 20:33:42.442 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:33:42.443 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:33:52.665 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:33:52.665 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:33:52.927 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:33:52.927 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:33:52.928 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:33:52.928 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]水平角测量主要目的是( )。...
2025-07-27 20:33:53.137 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:33:53.138 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:33:54.560 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 确定点的高程
2025-07-27 20:33:54.562 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A确定点的平面位置', 'B': 'B确定点的高程', 'C': 'C确定水平距离', 'D': 'D确定高差'}
2025-07-27 20:33:54.562 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 确定点的高程
2025-07-27 20:33:54.562 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:33:54.563 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:33:54.563 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 1/28 个题目
2025-07-27 20:33:54.563 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/28 个题目
2025-07-27 20:33:54.564 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:33:54.565 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:34:04.719 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:34:04.719 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:34:05.009 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:34:05.010 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:34:05.010 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:34:05.011 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪不能直接用于测量( )。...
2025-07-27 20:34:05.221 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:34:05.222 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:34:06.988 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 点的坐标
2025-07-27 20:34:06.989 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A点的坐标', 'B': 'B水平角', 'C': 'C垂直角', 'D': 'D视距'}
2025-07-27 20:34:06.990 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 点的坐标
2025-07-27 20:34:06.990 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 20:34:06.990 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: A
2025-07-27 20:34:06.991 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 2/28 个题目
2025-07-27 20:34:06.991 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/28 个题目
2025-07-27 20:34:06.992 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:34:06.993 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪基座上有三个脚螺旋,其主要作用是( )。...
2025-07-27 20:34:12.989 | SUCCESS  | API.Questionbank:questionbank:129 - 主题库查询成功: 精确整平仪器...
2025-07-27 20:34:12.990 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A连接脚架', 'B': 'B整平仪器', 'C': 'C升降脚架', 'D': 'D调节对中'}
2025-07-27 20:34:12.991 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 精确整平仪器
2025-07-27 20:34:12.991 | INFO     | API.WorkTask:Xuan:1159 - ID:***********,尝试使用相似度匹配
2025-07-27 20:34:12.992 | INFO     | API.WorkTask:Xuan:1179 - ID:***********,尝试使用关键词匹配，答案关键词: {'精确整平仪器'}
2025-07-27 20:34:12.994 | INFO     | API.WorkTask:Xuan:1228 - ID:***********,尝试使用短语匹配，答案短语: ['精确整平仪器']
2025-07-27 20:34:12.995 | ERROR    | API.WorkTask:Xuan:1336 - ID:***********,使用match_answer函数匹配失败: 'str' object has no attribute 'select_one'
2025-07-27 20:34:12.995 | WARNING  | API.WorkTask:Xuan:1340 - ID:***********,答案无法与任何选项匹配
2025-07-27 20:34:12.995 | WARNING  | API.WorkTask:Html_Wkrk:551 - ID:***********,主题库答案处理失败，尝试备用题库
2025-07-27 20:34:12.996 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:34:12.996 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪基座上有三个脚螺旋,其主要作用是( )。...
2025-07-27 20:34:13.264 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:34:13.265 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:34:14.641 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 整平仪器
2025-07-27 20:34:14.642 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A连接脚架', 'B': 'B整平仪器', 'C': 'C升降脚架', 'D': 'D调节对中'}
2025-07-27 20:34:14.642 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 整平仪器
2025-07-27 20:34:14.643 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:34:14.643 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:34:14.643 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 3/28 个题目
2025-07-27 20:34:14.643 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/28 个题目
2025-07-27 20:34:14.645 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:34:14.645 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:34:24.774 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:34:24.774 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:34:25.013 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:34:25.014 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:34:25.014 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:34:25.014 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪精平操作应( )。...
2025-07-27 20:34:25.189 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:34:25.189 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:34:26.651 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 调节脚螺旋
2025-07-27 20:34:26.652 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A升降脚架', 'B': 'B调节脚螺旋', 'C': 'C调整脚架位置', 'D': 'D平移仪器'}
2025-07-27 20:34:26.652 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 调节脚螺旋
2025-07-27 20:34:26.653 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:34:26.653 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:34:26.653 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 4/28 个题目
2025-07-27 20:34:26.654 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/28 个题目
2025-07-27 20:34:26.655 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:34:26.655 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:34:35.098 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:34:36.804 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:34:36.804 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:34:37.052 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:34:37.053 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:34:37.053 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:34:37.054 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪的视准轴是( )。...
2025-07-27 20:34:37.234 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:34:37.234 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:34:38.650 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 望远镜物镜光心与十字丝中心的连线
2025-07-27 20:34:38.651 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A望远镜物镜光心与目镜光心的连线', 'B': 'B望远镜物镜光心与十字丝中心的连线', 'C': 'C望远镜目镜光心与十字丝中心的连线', 'D': 'D通过水准管内壁圆弧中点的切线'}
2025-07-27 20:34:38.651 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 望远镜物镜光心与十字丝中心的连线
2025-07-27 20:34:38.652 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:34:38.652 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:34:38.652 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 5/28 个题目
2025-07-27 20:34:38.653 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 6/28 个题目
2025-07-27 20:34:38.654 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:34:38.655 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:34:48.688 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:34:48.688 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:34:48.893 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:34:48.894 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:34:48.894 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:34:48.894 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪的粗平操作应( )。...
2025-07-27 20:34:49.080 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:34:49.080 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:34:51.423 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 平移脚架
2025-07-27 20:34:51.424 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A伸缩脚架', 'B': 'B平移脚架', 'C': 'C调节脚螺旋', 'D': 'D平移仪器'}
2025-07-27 20:34:51.425 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 平移脚架
2025-07-27 20:34:51.425 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:34:51.425 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:34:51.425 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 6/28 个题目
2025-07-27 20:34:51.426 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 7/28 个题目
2025-07-27 20:34:51.427 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:34:51.427 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标读数,其原因在于( )。...
2025-07-27 20:35:01.558 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:35:01.558 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标读数,其原因在于( )。...
2025-07-27 20:35:01.796 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:35:01.796 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:35:01.797 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:35:01.797 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]测回法测量水平角,计算角度总是用右目标读数减左目标...
2025-07-27 20:35:01.989 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:35:01.990 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:35:08.211 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 水平度盘刻度是顺时针增加的
2025-07-27 20:35:08.212 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A水平度盘刻度是顺时针增加的', 'B': 'B右目标读数大,左目标读数小', 'C': 'C水平度盘刻度是逆时针增加的', 'D': 'D倒过来减可能得负数'}
2025-07-27 20:35:08.212 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 水平度盘刻度是顺时针增加的
2025-07-27 20:35:08.213 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: A
2025-07-27 20:35:08.213 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: A
2025-07-27 20:35:08.213 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 7/28 个题目
2025-07-27 20:35:08.214 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 8/28 个题目
2025-07-27 20:35:08.215 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:35:08.216 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°28′13″,下半测回角值为102°28′...
2025-07-27 20:35:18.373 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:35:18.373 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°28′13″,下半测回角值为102°28′...
2025-07-27 20:35:18.580 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:35:18.580 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:35:18.581 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:35:18.581 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]测回法观测某水平角一测回,上半测回角值为102°2...
2025-07-27 20:35:18.790 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:35:18.791 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:35:34.034 | WARNING  | API.WorkTask:get_ai_answer_for_choice:2006 - ID:***********,AI接口返回错误: API请求失败: HTTP码=0
2025-07-27 20:35:34.035 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 8/28 个题目
2025-07-27 20:35:34.035 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 9/28 个题目
2025-07-27 20:35:34.037 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:35:34.037 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是为了消除( )误差对测角的影响。...
2025-07-27 20:35:35.099 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:35:44.222 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:35:44.222 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是为了消除( )误差对测角的影响。...
2025-07-27 20:35:44.417 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:35:44.418 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:35:44.418 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:35:44.418 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]用经纬仪观测水平角时,尽量照准目标的底部,其目的是...
2025-07-27 20:35:44.590 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:35:44.591 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:35:45.887 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 照准
2025-07-27 20:35:45.889 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A对中', 'B': 'B照准', 'C': 'C目标偏离中心', 'D': 'D整平'}
2025-07-27 20:35:45.889 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 照准
2025-07-27 20:35:45.889 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:35:45.890 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:35:45.890 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 9/28 个题目
2025-07-27 20:35:45.890 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 10/28 个题目
2025-07-27 20:35:45.892 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:35:45.892 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:35:56.017 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:35:56.017 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:35:56.217 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:35:56.218 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:35:56.218 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:35:56.218 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]倾斜视线在水平视线的上方,则该垂直角( )。...
2025-07-27 20:35:56.410 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:35:56.411 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:35:57.760 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 称为仰角,角值为正
2025-07-27 20:35:57.760 | INFO     | API.WorkTask:get_ai_answer_for_choice:1892 - ID:***********,检测到','分隔的多选题答案，转换为###分隔
2025-07-27 20:35:57.761 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1954 - ID:***********,多选题匹配成功: A
2025-07-27 20:35:57.761 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 10/28 个题目
2025-07-27 20:35:57.761 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 11/28 个题目
2025-07-27 20:35:57.763 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:35:57.763 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90°,望远镜物镜端抬高时读数减少,其盘左的...
2025-07-27 20:36:07.820 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:36:07.820 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90°,望远镜物镜端抬高时读数减少,其盘左的...
2025-07-27 20:36:08.035 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:36:08.036 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:36:08.036 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:36:08.037 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪在盘左位置时将望远镜置平,使其竖盘读数为90...
2025-07-27 20:36:08.231 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:36:08.231 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:36:09.865 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: L-90°
2025-07-27 20:36:09.866 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A90°-L', 'B': 'BL-90°', 'C': 'C180°-L', 'D': 'DL-180°'}
2025-07-27 20:36:09.867 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: L-90°
2025-07-27 20:36:09.867 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:36:09.867 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:36:09.868 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 11/28 个题目
2025-07-27 20:36:09.868 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 12/28 个题目
2025-07-27 20:36:09.870 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:36:09.870 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 20:36:20.023 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:36:20.023 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 20:36:20.251 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:36:20.251 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:36:20.252 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:36:20.252 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101...
2025-07-27 20:36:20.443 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:36:20.444 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:36:21.725 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: -12″
2025-07-27 20:36:21.726 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A+24″', 'B': 'B-12″', 'C': 'C-24″', 'D': 'D+12″'}
2025-07-27 20:36:21.726 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: -12″
2025-07-27 20:36:21.727 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:36:21.727 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:36:21.727 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 12/28 个题目
2025-07-27 20:36:21.727 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 13/28 个题目
2025-07-27 20:36:21.729 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:36:21.729 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 20:36:31.896 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:36:31.896 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 20:36:32.133 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:36:32.133 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:36:32.134 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:36:32.134 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°4...
2025-07-27 20:36:32.300 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:36:32.300 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:36:33.674 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: +06″
2025-07-27 20:36:33.675 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A-06″', 'B': 'B+06″', 'C': 'C-12″', 'D': 'D+12″'}
2025-07-27 20:36:33.676 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: +06″
2025-07-27 20:36:33.676 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:36:33.676 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:36:33.677 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 13/28 个题目
2025-07-27 20:36:33.677 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 14/28 个题目
2025-07-27 20:36:33.678 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:36:33.679 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:36:35.100 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:36:43.798 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:36:43.799 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:36:44.003 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:36:44.003 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:36:44.003 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:36:44.004 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]在工程测量中,角度测量包括( )。...
2025-07-27 20:36:44.211 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:36:44.212 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:36:45.560 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 水平角测量
垂直角测量
2025-07-27 20:36:45.561 | INFO     | API.WorkTask:get_ai_answer_for_choice:1883 - ID:***********,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-27 20:36:45.561 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1954 - ID:***********,多选题匹配成功: A
2025-07-27 20:36:45.561 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 14/28 个题目
2025-07-27 20:36:45.562 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 15/28 个题目
2025-07-27 20:36:45.563 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:36:45.563 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:36:55.726 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:36:55.726 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:36:55.990 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:36:55.990 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:36:55.991 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:36:55.991 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]经纬仪整平的目的是( )。...
2025-07-27 20:36:56.176 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:36:56.176 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:36:57.440 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 使水平度盘水平
2025-07-27 20:36:57.441 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A使竖轴处于铅垂位置', 'B': 'B使水平度盘水平', 'C': 'C使横轴处于水平位置', 'D': 'D使竖轴位于竖直度盘铅垂面内', 'E': 'E使仪器中心与测站点标志中心位于同一铅垂线上'}
2025-07-27 20:36:57.441 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 使水平度盘水平
2025-07-27 20:36:57.441 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:36:57.441 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:36:57.442 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 15/28 个题目
2025-07-27 20:36:57.442 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 16/28 个题目
2025-07-27 20:36:57.443 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:36:57.444 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,这一操作不能消除或减弱的误差包括( )。...
2025-07-27 20:37:07.599 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:37:07.599 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,这一操作不能消除或减弱的误差包括( )。...
2025-07-27 20:37:07.863 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:37:07.863 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:37:07.863 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:37:07.864 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]测回法采用盘左和盘右观测角值取平均作为一测回角值,...
2025-07-27 20:37:08.024 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:37:08.024 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:37:09.579 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 度盘刻划误差
2025-07-27 20:37:09.580 | INFO     | API.WorkTask:Xuan:989 - ID:***********,找到选项: {'A': 'A横轴误差', 'B': 'B度盘刻划误差', 'C': 'C视准轴误差', 'D': 'D竖轴误差', 'E': 'E仪器对中误差'}
2025-07-27 20:37:09.581 | INFO     | API.WorkTask:Xuan:990 - ID:***********,题库答案: 度盘刻划误差
2025-07-27 20:37:09.581 | SUCCESS  | API.WorkTask:Xuan:1120 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 20:37:09.581 | INFO     | API.WorkTask:get_ai_answer_for_choice:1970 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 20:37:09.581 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 16/28 个题目
2025-07-27 20:37:09.581 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 17/28 个题目
2025-07-27 20:37:09.583 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:37:09.583 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部旋转方向说法正确的是( )。...
2025-07-27 20:37:19.702 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:37:19.702 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部旋转方向说法正确的是( )。...
2025-07-27 20:37:19.936 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:37:19.937 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:37:19.937 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:37:19.938 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [多选题]测回法观测水平角时,照准不同方向的目标,对于照准部...
2025-07-27 20:37:20.141 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:37:20.141 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:37:22.047 | INFO     | API.WorkTask:get_ai_answer_for_choice:1878 - ID:***********,AI返回的答案内容: 盘左顺时针旋转
盘右逆时针旋转
2025-07-27 20:37:22.047 | INFO     | API.WorkTask:get_ai_answer_for_choice:1883 - ID:***********,检测到换行符分隔的多选题答案，转换为###分隔
2025-07-27 20:37:22.047 | SUCCESS  | API.WorkTask:get_ai_answer_for_choice:1954 - ID:***********,多选题匹配成功: A
2025-07-27 20:37:22.047 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 17/28 个题目
2025-07-27 20:37:22.048 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 18/28 个题目
2025-07-27 20:37:22.049 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:37:22.049 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:37:32.101 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:37:32.101 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:37:32.355 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:37:32.356 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:37:32.356 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:37:32.356 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]光学经纬仪的构造由()、()、()三部分组成...
2025-07-27 20:37:32.544 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:37:32.545 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:37:33.919 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_blank:2070 - ID:***********,AI生成填空题答案: 照准部###水平度盘###基座
2025-07-27 20:37:33.919 | SUCCESS  | API.WorkTask:Html_Wkrk:692 - ID:***********,AI生成填空题答案成功
2025-07-27 20:37:33.920 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 18/28 个题目
2025-07-27 20:37:33.920 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 19/28 个题目
2025-07-27 20:37:33.921 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:37:33.922 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:37:35.101 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:37:44.085 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:37:44.085 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:37:44.314 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:37:44.315 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:37:44.315 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:37:44.315 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]光学经纬仪的读数设备主要有()、()、()...
2025-07-27 20:37:44.507 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:37:44.508 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:37:46.403 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_blank:2070 - ID:***********,AI生成填空题答案: 游标###光学测微器###电子读数系统
2025-07-27 20:37:46.403 | SUCCESS  | API.WorkTask:Html_Wkrk:692 - ID:***********,AI生成填空题答案成功
2025-07-27 20:37:46.404 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 19/28 个题目
2025-07-27 20:37:46.404 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 20/28 个题目
2025-07-27 20:37:46.405 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:37:46.406 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步骤...
2025-07-27 20:37:56.555 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:37:56.555 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步骤...
2025-07-27 20:37:56.780 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:37:56.781 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:37:56.781 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:37:56.781 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [填空题]经纬仪的使用,一般分为()、()、()、()四个步...
2025-07-27 20:37:56.971 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:37:56.972 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:37:58.294 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_blank:2070 - ID:***********,AI生成填空题答案: 对中###整平###瞄准###读数
2025-07-27 20:37:58.295 | SUCCESS  | API.WorkTask:Html_Wkrk:692 - ID:***********,AI生成填空题答案成功
2025-07-27 20:37:58.295 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 20/28 个题目
2025-07-27 20:37:58.296 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 21/28 个题目
2025-07-27 20:37:58.297 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:37:58.297 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:38:08.361 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:38:08.361 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:38:08.589 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:38:08.589 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:38:08.590 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:38:08.590 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 根据垂直角观测的原理,垂直角的最大值为度...
2025-07-27 20:38:08.797 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:38:08.798 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:38:10.232 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 20:38:10.233 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 21/28 个题目
2025-07-27 20:38:10.233 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 22/28 个题目
2025-07-27 20:38:10.234 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:38:10.234 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 20:38:20.376 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:38:20.376 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 20:38:20.598 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:38:20.599 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:38:20.599 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:38:20.599 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加...
2025-07-27 20:38:20.785 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:38:20.785 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:38:22.079 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 20:38:22.080 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 22/28 个题目
2025-07-27 20:38:22.080 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 23/28 个题目
2025-07-27 20:38:22.082 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:38:22.082 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:38:32.269 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:38:32.269 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:38:32.513 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:38:32.513 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:38:32.513 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:38:32.514 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [简答题] 经纬仪对中的目的是什么...
2025-07-27 20:38:32.690 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:38:32.691 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:38:34.112 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2197 - ID:***********,AI生成简答题答案: 使仪器中心与测站点位于同一铅垂线上。...
2025-07-27 20:38:34.112 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:38:34.112 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 23/28 个题目
2025-07-27 20:38:34.113 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 24/28 个题目
2025-07-27 20:38:34.114 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:38:34.114 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:38:35.102 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:38:44.161 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:38:44.161 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:38:44.365 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:38:44.366 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:38:44.366 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:38:44.366 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [简答题] 经纬仪整平的目的是什么...
2025-07-27 20:38:44.569 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:38:44.570 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:38:46.155 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2197 - ID:***********,AI生成简答题答案: 使仪器竖轴处于铅垂位置，水平度盘处于水平状态。...
2025-07-27 20:38:46.156 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:38:46.156 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 24/28 个题目
2025-07-27 20:38:46.156 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 25/28 个题目
2025-07-27 20:38:46.158 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:38:46.158 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 测回法...
2025-07-27 20:38:56.297 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:38:56.297 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 测回法...
2025-07-27 20:38:56.556 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:38:56.557 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:38:56.557 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:38:56.557 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 测回法...
2025-07-27 20:38:56.745 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:38:56.746 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:38:58.421 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2197 - ID:***********,AI生成简答题答案: 测回法是一种角度测量方法，通过多次观测取平均值提高精度。...
2025-07-27 20:38:58.422 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:38:58.422 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 25/28 个题目
2025-07-27 20:38:58.422 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 26/28 个题目
2025-07-27 20:38:58.424 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:38:58.424 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 盘左...
2025-07-27 20:39:08.456 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:39:08.456 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 盘左...
2025-07-27 20:39:08.703 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:39:08.704 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:39:08.704 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:39:08.704 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 盘左...
2025-07-27 20:39:08.867 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:39:08.868 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:39:24.088 | WARNING  | API.WorkTask:_generate_ai_answer_for_essay:2202 - ID:***********,AI接口返回错误: API请求失败: HTTP码=0
2025-07-27 20:39:24.088 | WARNING  | API.WorkTask:Html_Wkrk:794 - ID:***********,AI答题失败，跳过此题
2025-07-27 20:39:24.089 | WARNING  | API.WorkTask:Html_Wkrk:811 - ID:***********,所有答题方法均失败，跳过此题
2025-07-27 20:39:24.089 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 27/28 个题目
2025-07-27 20:39:24.091 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:39:24.091 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [名词解释] 水平角...
2025-07-27 20:39:34.233 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:39:34.233 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [名词解释] 水平角...
2025-07-27 20:39:34.448 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:39:34.449 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:39:34.449 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:39:34.449 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [名词解释] 水平角...
2025-07-27 20:39:34.643 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:39:34.644 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:39:35.103 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 20:39:49.850 | WARNING  | API.WorkTask:_generate_ai_answer_for_essay:2202 - ID:***********,AI接口返回错误: API请求失败: HTTP码=0
2025-07-27 20:39:49.851 | WARNING  | API.WorkTask:Html_Wkrk:794 - ID:***********,AI答题失败，跳过此题
2025-07-27 20:39:49.852 | WARNING  | API.WorkTask:Html_Wkrk:811 - ID:***********,所有答题方法均失败，跳过此题
2025-07-27 20:39:49.852 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 28/28 个题目
2025-07-27 20:39:49.853 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 20:39:49.853 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:39:59.982 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 20:39:59.982 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:40:00.232 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 20:40:00.233 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 20:40:00.234 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 20:40:00.234 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [论述题] 简述经纬仪操作过程...
2025-07-27 20:40:00.432 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 20:40:00.433 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 20:40:04.747 | SUCCESS  | API.WorkTask:_generate_ai_answer_for_essay:2197 - ID:***********,AI生成简答题答案: 1. **安置仪器**：将经纬仪架设在三脚架上，调整脚架高度，确保仪器大致水平。  
2. **粗略...
2025-07-27 20:40:04.747 | SUCCESS  | API.WorkTask:Html_Wkrk:770 - ID:***********,AI生成简答题答案成功
2025-07-27 20:40:04.748 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 26/28 个题目
2025-07-27 20:40:04.748 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 26/28 个
2025-07-27 20:40:04.748 | WARNING  | API.WorkTask:Html_Wkrk:857 - ID:***********,答题不完整(26/28)，使用保存模式
2025-07-27 20:40:05.253 | SUCCESS  | API.WorkTask:PostDoChapterTest:1705 - ID:***********,章节测验保存成功: {"msg":"无效的参数：code-2！","status":false}
2025-07-27 20:40:07.756 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 3.6 单元测验 完成，等待 75 秒后处理下一章节
2025-07-27 20:40:34.582 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
