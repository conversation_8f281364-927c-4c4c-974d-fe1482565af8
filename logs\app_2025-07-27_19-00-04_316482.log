2025-07-27 19:00:04.317 | INFO     | __main__:<module>:1894 - 学习通自动化系统启动...
2025-07-27 19:00:04.317 | SUCCESS  | Config.UserSql:__init__:22 - 数据库连接池初始化成功
2025-07-27 19:00:04.317 | SUCCESS  | __main__:<module>:1904 - 数据库连接池初始化成功
2025-07-27 19:00:04.320 | SUCCESS  | __main__:<module>:1925 - 数据状态重置完成
2025-07-27 19:00:04.320 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 0/100
2025-07-27 19:00:04.321 | INFO     | __main__:<module>:1941 - 开始处理订单...
2025-07-27 19:00:04.321 | INFO     | __main__:order_get:1697 - 订单处理线程启动，最大线程数: 100
2025-07-27 19:00:04.322 | INFO     | __main__:order_get:1722 - 获取到 1 个待处理订单，当前活跃线程数: 0
2025-07-27 19:00:04.323 | INFO     | __main__:Run:1781 - 开始处理订单: OID=297357, 用户=***********, 课程ID=*********
2025-07-27 19:00:04.323 | INFO     | __main__:register_thread:67 - 注册线程: OID=297357, 当前活跃线程数: 1
2025-07-27 19:00:04.825 | SUCCESS  | __main__:Run:1825 - ID:***********,登录成功
2025-07-27 19:00:04.833 | INFO     | __main__:kclist:254 - ID:***********,正在获取课程列表...
2025-07-27 19:00:05.295 | INFO     | __main__:kclist:256 - ID:***********,课程列表API返回: {'result': 1, 'msg': '获取成功', 'channelList': [{'cfid': -1, 'norder': 72, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108147120, 'content': {'studentcount': 35, 'chatid': '262452157677570', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王欣欣', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=247101786&personId=282371395&classId=108147120&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '建筑设备与识图', 'id': 247101786}]}, 'roletype': 3, 'id': 108147120, 'state': 0, 'cpi': 282371395, 'bbsid': '233d0c9384c7fd49ef30471e70b6f18d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 70, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 103243606, 'content': {'studentcount': 37, 'chatid': '257804644450310', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102班', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '9136', 'coursestate': 0, 'teacherfactor': '辛星、孙万香', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245076370&personId=306706095&classId=103243606&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '现代建筑施工项目管理', 'id': 245076370}]}, 'roletype': 3, 'id': 103243606, 'state': 0, 'cpi': 306706095, 'bbsid': '82e9a0452a5fb0153a08742d38d2e5d3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 62, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93174104, 'content': {'studentcount': 36, 'chatid': '240755038814211', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=240249074&personId=282371395&classId=93174104&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/669ca80d6a0c5f74835bb936a41aabca.jpg', 'name': '装配式构造与识图', 'id': 240249074}]}, 'roletype': 3, 'id': 93174104, 'state': 0, 'cpi': 282371395, 'bbsid': '9c443055acdff2d922c91a98f0d01497', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 61, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93031937, 'content': {'studentcount': 37, 'chatid': '240686109622276', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '乔颖秀', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241048997&personId=282371395&classId=93031937&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '建筑装饰施工技术', 'id': 241048997}]}, 'roletype': 3, 'id': 93031937, 'state': 0, 'cpi': 282371395, 'bbsid': '8bc656572d3aeb51ff07fd8df2167bde', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 58, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': ********, 'content': {'studentcount': 36, 'chatid': '235813675794433', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '6,9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王婧', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=*********&personId=282371395&classId=********&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/b34d7695e8fd9ce297ef09b2bcb31608.jpg', 'name': '工程测量', 'id': *********}]}, 'roletype': 3, 'id': ********, 'state': 0, 'cpi': 282371395, 'bbsid': 'bee20260b71f18a382f79be093c75781', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 50, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 84338229, 'content': {'studentcount': 36, 'chatid': '228028031500290', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵翠', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237732213&personId=282371395&classId=84338229&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '监理概论', 'id': 237732213}]}, 'roletype': 3, 'id': 84338229, 'state': 0, 'cpi': 282371395, 'bbsid': 'c7b537a1f1f64b3e1f029c0b1e6f3ac9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 41, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81695272, 'content': {'studentcount': 39, 'chatid': '225435820556293', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=222501937&personId=282371395&classId=81695272&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/02dd581c5cfd7d4840aa0924a5741b87.png', 'name': '砌体工程施工', 'id': 222501937}]}, 'roletype': 3, 'id': 81695272, 'state': 0, 'cpi': 282371395, 'bbsid': '50a0052e1faaf398eab9eed65fdcbbe6', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 34, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77731070, 'content': {'studentcount': 37, 'chatid': '214216739979265', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=206940210&personId=282371395&classId=77731070&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/5fcb560ba7bde03929ccae19113a260a.png', 'name': '房屋构造', 'id': 206940210}]}, 'roletype': 3, 'id': 77731070, 'state': 0, 'cpi': 282371395, 'bbsid': '727dcfd2ef24a37e905835bc220b2e79', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 29, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 73006297, 'content': {'studentcount': 36, 'chatid': '207071973801986', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232937843&personId=282371395&classId=73006297&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/445cfff6239083f79374551111fb385b.png', 'name': '二十四式简化太极拳', 'id': 232937843}]}, 'roletype': 3, 'id': 73006297, 'state': 0, 'cpi': 282371395, 'bbsid': 'bd43bf14e2bae80865bad5e04a164058', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 24, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63861990, 'content': {'studentcount': 56, 'chatid': '193216339050497', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102、装配22101', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王波', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228707968&personId=282371395&classId=63861990&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '应用数学A（一）', 'id': 228707968}]}, 'roletype': 3, 'id': 63861990, 'state': 0, 'cpi': 282371395, 'bbsid': 'd879253745a36fc8ffa778689174eb5f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 13, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64051718, 'content': {'studentcount': 38, 'chatid': '193380112990210', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张睿', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228856150&personId=282371395&classId=64051718&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/ee1c91e5d692ad719f7edd5c90a3625e.jpg', 'name': '2022级大学英语', 'id': 228856150}]}, 'roletype': 3, 'id': 64051718, 'state': 0, 'cpi': 282371395, 'bbsid': '1df2bd032967a8bdb8a0185c961aceff', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 12, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63990559, 'content': {'studentcount': 37, 'chatid': '193312997834753', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黎佳媚', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228506578&personId=239575512&classId=63990559&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生心理健康', 'id': 228506578}]}, 'roletype': 3, 'id': 63990559, 'state': 0, 'cpi': 239575512, 'bbsid': '63e85270f677fb0d9ca5bc5a66abcf82', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 11, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63680895, 'content': {'studentcount': 37, 'chatid': '193107346915329', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=219619084&personId=239575512&classId=63680895&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑力学与结构', 'id': 219619084}]}, 'roletype': 3, 'id': 63680895, 'state': 0, 'cpi': 239575512, 'bbsid': '83e4bf9de026eecc5d2c0344a0d72062', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 10, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 63759655, 'content': {'studentcount': 36, 'chatid': '193145009668098', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '崔炳文', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228721228&personId=239575512&classId=63759655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/3a7c4e2929905646967168696f78ff01.png', 'name': '大学生职业发展与就业指导', 'id': 228721228}]}, 'roletype': 3, 'id': 63759655, 'state': 0, 'cpi': 239575512, 'bbsid': '4247ba6c3c91d5fe2b0847f4fb2dab18', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 3, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 63765619, 'content': {'studentcount': 36, 'chatid': '193151980601345', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '赵亮', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228724038&personId=282371395&classId=63765619&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/4d22019e5c81452b5fc4082ac48a0fc0.png', 'name': '体育与健康课-西职院-建筑与轨道交通学院2022级', 'id': 228724038}]}, 'roletype': 3, 'id': 63765619, 'state': 0, 'cpi': 282371395, 'bbsid': 'bfb1bf61cb93753dc55127646480fb23', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 2, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 64009643, 'content': {'studentcount': 35, 'chatid': '193329581064194', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 0, 'smartCourseState': 0, 'appData': 0, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王觅', 'isCourseSquare': 0, 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=228836418&personId=282371395&classId=64009643&userId=221880762', 'imageurl': 'http://p.ananas.chaoxing.com/star3/origin/f23a4b313e1aa0a7a840ab81aabc918f.jpg', 'name': '建筑工程识图（1+X）', 'id': 228836418}]}, 'roletype': 3, 'id': 64009643, 'state': 0, 'cpi': 282371395, 'bbsid': 'c974502359bdde67e6a49c879e1c5a4c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 71, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 108143976, 'content': {'studentcount': 38, 'chatid': '262448649142274', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月  王成平 吉海军 孟琳 牛欣欣       西安职业技术学院', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=245183825&personId=282371395&classId=108143976&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/00ceb684f7b286cb38e7a610330ee3bb.png', 'name': '建筑类专业创新创业基础', 'id': 245183825}]}, 'roletype': 3, 'id': 108143976, 'state': 1, 'cpi': 282371395, 'bbsid': 'a61bd4f169036c406137776a7e69edd4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 67, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 99702566, 'content': {'studentcount': 31, 'chatid': '249557002813441', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙万香、张阳、乔颖秀，李頔、刘颖、伦琳琳、吕萍', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=242066105&personId=282371395&classId=99702566&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/e15f9d0fa84e0dc88cf6bee453692061.jpg', 'name': '居住空间设计', 'id': 242066105}]}, 'roletype': 3, 'id': 99702566, 'state': 1, 'cpi': 282371395, 'bbsid': 'f7f88c1f5d8e2510715d9d40c0f52c11', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 66, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 98878754, 'content': {'studentcount': 36, 'chatid': '247959327408129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241528817&personId=282371395&classId=98878754&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 241528817}]}, 'roletype': 3, 'id': 98878754, 'state': 1, 'cpi': 282371395, 'bbsid': 'bc75f264d544b6db51bc97e06a2dbffb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 65, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 96477120, 'content': {'studentcount': 66, 'chatid': '243660016910338', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22101班+22102班（张莹霞）', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '高静娜王世伟王颖易云王虹田雨李芮王怡玮张莹霞何慧琦刘景华马岩李晓航范建梅李殷红郭炯张哲王帅军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241400281&personId=282371395&classId=96477120&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/411a62e07ee35beb471ff8591e7c0583.png', 'name': '习近平新时代中国特色社会主义思想概论', 'id': 241400281}]}, 'roletype': 3, 'id': 96477120, 'state': 1, 'cpi': 282371395, 'bbsid': 'a31e26a3f7dcfe346e427926a0c12843', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 64, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 94747290, 'content': {'studentcount': 36, 'chatid': '241659454488581', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241352309&personId=282371395&classId=94747290&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 241352309}]}, 'roletype': 3, 'id': 94747290, 'state': 1, 'cpi': 282371395, 'bbsid': '8615728073e0266575a4f2634f73f4b4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 63, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 93458508, 'content': {'studentcount': 37, 'chatid': '240865358446597', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '王成平', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=241226635&personId=282371395&classId=93458508&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0078f13df62d385adbf64bd357801250.jpg', 'name': '装配式建筑构件制作与安装（1+X）', 'id': 241226635}]}, 'roletype': 3, 'id': 93458508, 'state': 1, 'cpi': 282371395, 'bbsid': '54f658b680b207e33f03dc30f26eaaf9', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 57, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 90776227, 'content': {'studentcount': 36, 'chatid': '235091979730946', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李佳凌', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=239229163&personId=282371395&classId=90776227&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/a96505143b23aa921668b4c195fced6b.png', 'name': '工程监理概论', 'id': 239229163}]}, 'roletype': 3, 'id': 90776227, 'state': 1, 'cpi': 282371395, 'bbsid': '3fa527de268d07e7f56fd000639a6280', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 56, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86627150, 'content': {'studentcount': 39, 'chatid': '230251015766019', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257713&personId=282371395&classId=86627150&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 236257713}]}, 'roletype': 3, 'id': 86627150, 'state': 1, 'cpi': 282371395, 'bbsid': 'c9e6fc8ae6f2a130c48c7e7781f42187', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 55, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 86450001, 'content': {'studentcount': 36, 'chatid': '230515973095428', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236257673&personId=282371395&classId=86450001&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 236257673}]}, 'roletype': 3, 'id': 86450001, 'state': 1, 'cpi': 282371395, 'bbsid': 'ccf2d9d3dd2cdde64beec68109dea4b0', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 53, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568471, 'content': {'studentcount': 184, 'chatid': '226902282403844', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '黄玉顺', 'isCourseSquare': 0, 'schools': '山东大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417781&personId=282371395&classId=83568471&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cac10875886f12e31fa939a4b330acc2.jpg', 'name': '儒学与生活', 'id': 237417781}]}, 'roletype': 3, 'id': 83568471, 'state': 1, 'cpi': 282371395, 'bbsid': 'd5b6945eac682309e1c07aaf36380f67', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 52, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568466, 'content': {'studentcount': 84, 'chatid': '226902281355269', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '孙劲松 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417778&personId=282371395&classId=83568466&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/cdcd8e1a0722b927b2bd2370ab0ca37f.jpg', 'name': '《周易》的奥秘', 'id': 237417778}]}, 'roletype': 3, 'id': 83568466, 'state': 1, 'cpi': 282371395, 'bbsid': '3905ad7fe8b5446aa42aba53526e017c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 51, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83568419, 'content': {'studentcount': 186, 'chatid': '226902271918084', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李义平', 'isCourseSquare': 0, 'schools': '中国人民大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237417727&personId=282371395&classId=83568419&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star/270_169c/1395222258227jqpbs.png', 'name': '经济学百年', 'id': 237417727}]}, 'roletype': 3, 'id': 83568419, 'state': 1, 'cpi': 282371395, 'bbsid': '8c3ac1bcb7e980cd757784e60d19c2f1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 48, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83894032, 'content': {'studentcount': 34, 'chatid': '227791773696001', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '袁敏、李缪美、冯三琴、陈萌、赵亚楠', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236205491&personId=282371395&classId=83894032&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/2b4f778a10dc049954c0703905744ed6.jpg', 'name': '中华优秀传统文化', 'id': 236205491}]}, 'roletype': 3, 'id': 83894032, 'state': 1, 'cpi': 282371395, 'bbsid': '2ef8c7abe16df4475e4c3608820689e1', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 47, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83565558, 'content': {'studentcount': 3241, 'chatid': '226899079004165', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '刘剑 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363016&personId=282371395&classId=83565558&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/033b195bb4d859c34f837436a2e2b047.png', 'name': '职业生涯提升', 'id': 237363016}]}, 'roletype': 3, 'id': 83565558, 'state': 1, 'cpi': 282371395, 'bbsid': 'da7fb09f3a83f26e851457f5b6c7139c', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 46, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 83563993, 'content': {'studentcount': 3241, 'chatid': '226898253774851', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '2022级三年制高职学生', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=237363015&personId=282371395&classId=83563993&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 237363015}]}, 'roletype': 3, 'id': 83563993, 'state': 1, 'cpi': 282371395, 'bbsid': '32e5fc34f009a71da5533841a69b8d4f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 44, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 82934974, 'content': {'studentcount': 37, 'chatid': '226347724111878', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '马月', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=236255632&personId=282371395&classId=82934974&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6009fdc4c2e35d0a875d3eba97e569a5.png', 'name': '工程经济', 'id': 236255632}]}, 'roletype': 3, 'id': 82934974, 'state': 1, 'cpi': 282371395, 'bbsid': '0930cc6c545794f09023829842e80bbb', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 42, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 81792501, 'content': {'studentcount': 40, 'chatid': '225462012936193', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225074231&personId=282371395&classId=81792501&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ccf2e893a0fec74beb2c807bed1380fe.png', 'name': '钢筋混凝土工程施工', 'id': 225074231}]}, 'roletype': 3, 'id': 81792501, 'state': 1, 'cpi': 282371395, 'bbsid': 'aab65d5d802d23a526d71e6cf24fa6ec', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 40, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 306706095, 'key': 79340160, 'content': {'studentcount': 306, 'chatid': '218929127555073', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '2023春校内选课4班', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '关蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232872307&personId=306706095&classId=79340160&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/45d95091261bf5413d34711b4812f878.png', 'name': '城市轨道交通行车组织', 'id': 232872307}]}, 'roletype': 3, 'id': 79340160, 'state': 1, 'cpi': 306706095, 'bbsid': 'ada486380464473062934e91bb473e95', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 35, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 78162387, 'content': {'studentcount': 101, 'chatid': '215496757673985', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '1+X培训', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=78162387&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 78162387, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 33, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 77274590, 'content': {'studentcount': 36, 'chatid': '212705581531137', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=234757681&personId=282371395&classId=77274590&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 234757681}]}, 'roletype': 3, 'id': 77274590, 'state': 1, 'cpi': 282371395, 'bbsid': 'bbf8dfaeefbe4f0bfca5f6c25be6c68d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 31, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74231723, 'content': {'studentcount': 37, 'chatid': '208325644976129', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张丹', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233334480&personId=282371395&classId=74231723&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/2872b2f652f8b035fee131317650d315.png', 'name': '建筑工程信息化（BIM5D）', 'id': 233334480}]}, 'roletype': 3, 'id': 74231723, 'state': 1, 'cpi': 282371395, 'bbsid': 'ac44ce09c57beb7098f3962568c9b5da', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 30, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 74122655, 'content': {'studentcount': 38, 'chatid': '208076318769153', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=233076005&personId=282371395&classId=74122655&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 233076005}]}, 'roletype': 3, 'id': 74122655, 'state': 1, 'cpi': 282371395, 'bbsid': 'e18b21fa283bde3c284082489b2e2d9d', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 28, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 72909517, 'content': {'studentcount': 42, 'chatid': '207009144176641', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 0, 'name': '建筑22102', 'course': {'data': [{'appInfo': '9', 'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '吉海军', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=232915084&personId=282371395&classId=72909517&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/193_114c/8da13f152a75aa27fd646b620d4a785c.jpg', 'name': 'BIM技术Revit软件建模', 'id': 232915084}]}, 'roletype': 3, 'id': 72909517, 'state': 1, 'cpi': 282371395, 'bbsid': '37149f94ac3f555df5c8e9f9e7bba0c3', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 20, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 239575512, 'key': 69595761, 'content': {'studentcount': 37, 'chatid': '200442127646722', 'isFiled': 0, 'isthirdaq': 0, 'isstart': True, 'isretire': 1, 'name': '建筑22102', 'course': {'data': [{'defaultShowCatalog': 2, 'smartCourseState': 0, 'appData': 2, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李蕾', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=225425874&personId=239575512&classId=69595761&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/0d46edde937f6e38906c7dbfe15c8c86.png', 'name': 'BIM技术建筑制图与CAD', 'id': 225425874}]}, 'roletype': 3, 'id': 69595761, 'state': 1, 'cpi': 239575512, 'bbsid': 'b83a68cde1f0614c47b8e2cd2d0b6ccf', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 19, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66385034, 'content': {'studentcount': 103, 'chatid': '196295283245059', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '杨树山', 'isCourseSquare': 0, 'schools': '天津理工大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229863793&personId=282371395&classId=66385034&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/56d95359e4b0dfadae7a3f77.jpg', 'name': '漫画艺术欣赏与创作', 'id': 229863793}]}, 'roletype': 3, 'id': 66385034, 'state': 1, 'cpi': 282371395, 'bbsid': 'cee2bff5ffef3ec232d3843cdc413c6f', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 18, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66384671, 'content': {'studentcount': 50, 'chatid': '196295121764358', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 1, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '段鑫星', 'isCourseSquare': 0, 'schools': '中国矿业大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229864935&personId=282371395&classId=66384671&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/6a97d52937f928f858572fa3d0cc9e9a.jpg', 'name': '恋爱心理学', 'id': 229864935}]}, 'roletype': 3, 'id': 66384671, 'state': 1, 'cpi': 282371395, 'bbsid': 'd269da8fd0755fc780863bd28fd29ff4', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 17, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115796, 'content': {'studentcount': 3586, 'chatid': '195917631258628', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '张国清', 'isCourseSquare': 0, 'schools': '同济大学', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745592&personId=282371395&classId=66115796&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/f01bc30632e023f83b3e8879cdeea2c7.jpg', 'name': '军事理论', 'id': 229745592}]}, 'roletype': 3, 'id': 66115796, 'state': 1, 'cpi': 282371395, 'bbsid': '2d62f1844657fabcd6f9a66200efc9ce', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 16, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115802, 'content': {'studentcount': 3268, 'chatid': '195917634404353', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '李家华 等', 'isCourseSquare': 0, 'schools': '', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745593&personId=282371395&classId=66115802&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/origin/ae547c3a6923a45c1baceb4cea782408.png', 'name': '创新创业基础', 'id': 229745593}]}, 'roletype': 3, 'id': 66115802, 'state': 1, 'cpi': 282371395, 'bbsid': '3294c8c4ccdf002e427fba19c732b9c8', 'isSquare': 0}, 'topsign': 0}, {'cfid': -1, 'norder': 15, 'cataName': '课程', 'cataid': '100000002', 'id': 0, 'cpi': 282371395, 'key': 66115811, 'content': {'studentcount': 3268, 'chatid': '195917639647234', 'isFiled': 0, 'isthirdaq': 1, 'isstart': True, 'isretire': 0, 'name': '默认班级', 'course': {'data': [{'appInfo': '5', 'defaultShowCatalog': 1, 'smartCourseState': 0, 'appData': 1, 'belongSchoolId': '2790', 'coursestate': 0, 'teacherfactor': '齐晖 等', 'isCourseSquare': 0, 'schools': '中原工学院', 'courseSquareUrl': 'https://tsjy.chaoxing.com/plaza/app?courseId=229745598&personId=282371395&classId=66115811&userId=221880762', 'imageurl': 'https://p.ananas.chaoxing.com/star3/270_169c/560a0b0ee4b040cfea1a0f68.jpg', 'name': '大学计算机基础', 'id': 229745598}]}, 'roletype': 3, 'id': 66115811, 'state': 1, 'cpi': 282371395, 'bbsid': '015404e051dbfe6a573acaa45e6b29dc', 'isSquare': 0}, 'topsign': 0}], 'mcode': '-1', 'createcourse': 1, 'teacherEndCourse': 0, 'showEndCourse': 1, 'hasMore': False, 'stuEndCourse': 1}
2025-07-27 19:00:05.304 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 247101786, 目标课程ID: *********
2025-07-27 19:00:05.304 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245076370, 目标课程ID: *********
2025-07-27 19:00:05.304 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 240249074, 目标课程ID: *********
2025-07-27 19:00:05.304 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241048997, 目标课程ID: *********
2025-07-27 19:00:05.305 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: *********, 目标课程ID: *********
2025-07-27 19:00:05.305 | SUCCESS  | __main__:kclist:298 - ID:***********,成功匹配课程: {'kcname': '工程测量', 'courseid': '*********', 'clazzid': ********, 'cpi': 282371395}
2025-07-27 19:00:05.305 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237732213, 目标课程ID: *********
2025-07-27 19:00:05.305 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 222501937, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 206940210, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232937843, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228707968, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228856150, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228506578, 目标课程ID: *********
2025-07-27 19:00:05.306 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 219619084, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228721228, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228724038, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 228836418, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 245183825, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 242066105, 目标课程ID: *********
2025-07-27 19:00:05.307 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241528817, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241400281, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241352309, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 241226635, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 239229163, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257713, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236257673, 目标课程ID: *********
2025-07-27 19:00:05.308 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417781, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417778, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237417727, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236205491, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363016, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 237363015, 目标课程ID: *********
2025-07-27 19:00:05.309 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 236255632, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225074231, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232872307, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 234757681, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233334480, 目标课程ID: *********
2025-07-27 19:00:05.310 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 233076005, 目标课程ID: *********
2025-07-27 19:00:05.311 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 232915084, 目标课程ID: *********
2025-07-27 19:00:05.311 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 225425874, 目标课程ID: *********
2025-07-27 19:00:05.311 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229863793, 目标课程ID: *********
2025-07-27 19:00:05.311 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229864935, 目标课程ID: *********
2025-07-27 19:00:05.311 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745592, 目标课程ID: *********
2025-07-27 19:00:05.312 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745593, 目标课程ID: *********
2025-07-27 19:00:05.312 | INFO     | __main__:kclist:286 - ID:***********,找到课程ID: 229745598, 目标课程ID: *********
2025-07-27 19:00:05.312 | SUCCESS  | __main__:Run:1834 - ID:***********,课程信息匹配成功
2025-07-27 19:00:05.935 | SUCCESS  | __main__:studentcourse:757 - ID:***********,课件获取完成，总共获取到35个未完成章节
2025-07-27 19:00:06.554 | ERROR    | data.Porgres:get_xxt_jxz:120 - 解析视频信息失败: 'NoneType' object has no attribute 'text'
2025-07-27 19:00:07.679 | SUCCESS  | __main__:studentstudy:935 - ID:***********,进度:2%,详情:课程任务:36/109 | 章节测验: 14/49; | 实时执行：3.5 竖直角测量 (1/35) | 更新:2025-07-27 19:00:05
2025-07-27 19:00:21.473 | SUCCESS  | API.TaskDo:task:181 - ID:***********,准备执行测试
2025-07-27 19:00:22.399 | INFO     | API.WorkTask:Html_Wkrk:56 - ID:***********,当前平台ID: 9003
2025-07-27 19:00:22.399 | INFO     | API.WorkTask:Html_Wkrk:64 - ID:***********,平台ID 9003，启用AI答题功能
2025-07-27 19:00:22.400 | INFO     | API.WorkTask:Html_Wkrk:256 - ID:***********,找到5个题目
2025-07-27 19:00:22.400 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 1/5 个题目
2025-07-27 19:00:22.402 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 19:00:22.402 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]竖直指标水准管气泡居中的目的是( )。...
2025-07-27 19:00:32.577 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 19:00:32.577 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]竖直指标水准管气泡居中的目的是( )。...
2025-07-27 19:00:32.850 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 19:00:32.850 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 19:00:32.850 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 19:00:32.851 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]竖直指标水准管气泡居中的目的是( )。...
2025-07-27 19:00:33.054 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 19:00:33.054 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 19:00:34.399 | INFO     | API.WorkTask:get_ai_answer_for_choice:1829 - ID:***********,AI返回的答案内容: 使竖盘指标指向90°
2025-07-27 19:00:34.401 | INFO     | API.WorkTask:Xuan:982 - ID:***********,找到选项: {'A': 'A使竖盘处于铅垂位置', 'B': 'B使竖盘指标指向90°', 'C': 'C使竖盘指标指向270°', 'D': 'D使竖盘指标指向90°或270°'}
2025-07-27 19:00:34.401 | INFO     | API.WorkTask:Xuan:983 - ID:***********,题库答案: 使竖盘指标指向90°
2025-07-27 19:00:34.401 | SUCCESS  | API.WorkTask:Xuan:1113 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 19:00:34.401 | INFO     | API.WorkTask:get_ai_answer_for_choice:1921 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 19:00:34.402 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 1/5 个题目
2025-07-27 19:00:34.402 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 2/5 个题目
2025-07-27 19:00:34.404 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 19:00:34.404 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]当经纬仪的望远镜上下转动时,竖直度盘( )。...
2025-07-27 19:00:44.586 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 19:00:44.586 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]当经纬仪的望远镜上下转动时,竖直度盘( )。...
2025-07-27 19:00:44.825 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 19:00:44.826 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 19:00:44.826 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 19:00:44.827 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]当经纬仪的望远镜上下转动时,竖直度盘( )。...
2025-07-27 19:00:45.016 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 19:00:45.017 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 19:00:46.494 | INFO     | API.WorkTask:get_ai_answer_for_choice:1829 - ID:***********,AI返回的答案内容: 与望远镜相对运动
2025-07-27 19:00:46.495 | INFO     | API.WorkTask:Xuan:982 - ID:***********,找到选项: {'A': 'A不动', 'B': 'B与望远镜相对运动', 'C': 'C与望远镜一起转动', 'D': 'D无法确定'}
2025-07-27 19:00:46.496 | INFO     | API.WorkTask:Xuan:983 - ID:***********,题库答案: 与望远镜相对运动
2025-07-27 19:00:46.496 | SUCCESS  | API.WorkTask:Xuan:1113 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 19:00:46.496 | INFO     | API.WorkTask:get_ai_answer_for_choice:1921 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 19:00:46.497 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 2/5 个题目
2025-07-27 19:00:46.497 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 3/5 个题目
2025-07-27 19:00:46.498 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 19:00:46.499 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 19:00:56.644 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 19:00:56.644 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°47′24″和278°12′24″,其竖盘...
2025-07-27 19:00:56.874 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 19:00:56.875 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 19:00:56.875 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 19:00:56.876 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]经纬仪瞄准目标P,盘左盘右的竖盘读数分别为81°4...
2025-07-27 19:00:57.066 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 19:00:57.067 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 19:00:58.369 | INFO     | API.WorkTask:get_ai_answer_for_choice:1829 - ID:***********,AI返回的答案内容: +06″
2025-07-27 19:00:58.371 | INFO     | API.WorkTask:Xuan:982 - ID:***********,找到选项: {'A': 'A-06″', 'B': 'B+06″', 'C': 'C-12″', 'D': 'D+12″'}
2025-07-27 19:00:58.371 | INFO     | API.WorkTask:Xuan:983 - ID:***********,题库答案: +06″
2025-07-27 19:00:58.371 | SUCCESS  | API.WorkTask:Xuan:1113 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 19:00:58.372 | INFO     | API.WorkTask:get_ai_answer_for_choice:1921 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 19:00:58.372 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 3/5 个题目
2025-07-27 19:00:58.372 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 4/5 个题目
2025-07-27 19:00:58.374 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 19:00:58.374 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 19:01:04.321 | INFO     | __main__:monitor_thread:1935 - 当前活跃线程数: 1/100
2025-07-27 19:01:08.456 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 19:01:08.456 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101°23′36″,盘右读数为258°36′...
2025-07-27 19:01:08.664 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 19:01:08.664 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 19:01:08.665 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 19:01:08.665 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [单选题]观测某目标的竖直角(顺时针注记),盘左读数为101...
2025-07-27 19:01:08.865 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 19:01:08.866 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 19:01:10.286 | INFO     | API.WorkTask:get_ai_answer_for_choice:1829 - ID:***********,AI返回的答案内容: -12″
2025-07-27 19:01:10.287 | INFO     | API.WorkTask:Xuan:982 - ID:***********,找到选项: {'A': 'A+24″', 'B': 'B-12″', 'C': 'C-24″', 'D': 'D+12″'}
2025-07-27 19:01:10.287 | INFO     | API.WorkTask:Xuan:983 - ID:***********,题库答案: -12″
2025-07-27 19:01:10.288 | SUCCESS  | API.WorkTask:Xuan:1113 - ID:***********,答案与选项内容包含匹配: B
2025-07-27 19:01:10.288 | INFO     | API.WorkTask:get_ai_answer_for_choice:1921 - ID:***********,使用Xuan函数匹配成功: B
2025-07-27 19:01:10.288 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 4/5 个题目
2025-07-27 19:01:10.288 | INFO     | API.WorkTask:Html_Wkrk:262 - ID:***********,开始处理第 5/5 个题目
2025-07-27 19:01:10.289 | INFO     | API.WorkTask:Html_Wkrk:460 - ID:***********,尝试使用主题库查询答案
2025-07-27 19:01:10.289 | INFO     | API.Questionbank:questionbank:92 - 尝试使用主题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 19:01:20.500 | WARNING  | API.Questionbank:questionbank:149 - 主题库查询超时，尝试备用题库
2025-07-27 19:01:20.500 | INFO     | API.Questionbank:questionbank:158 - 尝试使用备用题库查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加得度。...
2025-07-27 19:01:20.731 | WARNING  | API.Questionbank:questionbank:190 - 备用题库查询未找到答案: 题库存在题目但无答案
2025-07-27 19:01:20.731 | WARNING  | API.Questionbank:questionbank:208 - 所有题库均未找到答案
2025-07-27 19:01:20.732 | INFO     | API.WorkTask:Html_Wkrk:565 - ID:***********,尝试使用备用题库查询答案
2025-07-27 19:01:20.732 | INFO     | API.Questionbank:questionbank2:247 - 尝试使用备用题库2查询: [判断题] 经纬仪测量竖直角时,盘左盘右读数的理论关系是相加...
2025-07-27 19:01:20.915 | WARNING  | API.Questionbank:questionbank2:278 - 备用题库2查询失败: 题库存在题目但无答案
2025-07-27 19:01:20.915 | INFO     | API.WorkTask:Html_Wkrk:658 - ID:***********,尝试使用AI生成答案
2025-07-27 19:01:22.258 | SUCCESS  | API.WorkTask:Html_Wkrk:755 - ID:***********,AI生成判断题答案成功
2025-07-27 19:01:22.259 | INFO     | API.WorkTask:Html_Wkrk:789 - ID:***********,已处理 5/5 个题目
2025-07-27 19:01:22.259 | INFO     | API.WorkTask:Html_Wkrk:842 - ID:***********,所有题目处理完成，共 5/5 个
2025-07-27 19:01:22.698 | SUCCESS  | API.WorkTask:PostDoChapterTest:1657 - ID:***********,章节测验提交成功: {"msg":"success!","stuStatus":4,"backUrl":"","url":"/mooc-ans/work/phone/work-relation?workId=4fa20079605643519057379cc92a5cb1&relationId=********&courseId=*********&clazzId=********&knowledgeId=*********&mooc=0&jobId=work-bdce92517ed941efafc1b6b95b82744b&enc=f10001a0d5fa657b37a63feb55cb1261&ut=s&originJobId=null","status":true}
2025-07-27 19:01:25.202 | INFO     | __main__:studentstudy:1126 - ID:***********,章节 3.5 竖直角测量 完成，等待 65 秒后处理下一章节
2025-07-27 19:01:28.238 | WARNING  | __main__:signal_handler:104 - 接收到终止信号，正在安全关闭...
